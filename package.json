{"name": "new_year_hoff", "version": "1.0.0", "main": "api/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node api/index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google/generative-ai": "^0.24.1", "@supabase/supabase-js": "^2.49.4", "@vercel/analytics": "^1.5.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "connect-pg-simple": "^10.0.0", "cookie-parser": "^1.4.6", "dotenv": "^17.0.0", "express": "^4.18.2", "express-session": "^1.18.1", "mammoth": "^1.9.1", "multer": "^1.4.5-lts.1", "node-fetch": "^2.6.1", "pdf-parse": "^1.1.1", "pg": "^8.14.1", "sharp": "^0.34.0", "uuid": "^11.0.5"}, "devDependencies": {"rimraf": "^6.0.1"}}