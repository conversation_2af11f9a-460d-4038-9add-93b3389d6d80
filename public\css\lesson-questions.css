/* Styling for lesson questions and images */

/* Image Loading Animation */
.question-image {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.question-image.loaded {
    opacity: 1;
}

.question-image-container {
    position: relative;
    min-height: 100px;
    background-color: #f0f2f5;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px 0;
    border-radius: 8px;
    overflow: hidden;
}

/* Lesson Image Placeholder */
#lesson-image {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

#lesson-image[src] {
    opacity: 1;
}

#lesson-image-container {
    background-color: #f0f2f5;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    position: relative;
}

/* Enhanced styling for options */
.option-row {
    margin: 8px 0;
    padding: 10px;
    border-radius: 8px;
    background-color: #f8f9fa;
    transition: background-color 0.2s;
}

.option-row:hover {
    background-color: #e9ecef;
}

.option-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    width: 100%;
}

.option-letter {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background-color: #e9ecef;
    border-radius: 50%;
    margin-right: 10px;
    font-weight: bold;
    color: #495057;
}

.option-text {
    flex: 1;
}

/* Submit button styling */
#submit-quiz-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    color: #fff;
    background-color: #4CAF50;
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 999;
}

#submit-quiz-btn:hover {
    background-color: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.25);
}

#submit-quiz-btn:active {
    transform: translateY(0);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

/* Add space at the bottom of the page to prevent button from overlapping content */
body {
    padding-bottom: 80px;
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
    #submit-quiz-btn {
        position: fixed;
        bottom: 20px;
        left: 20px;
        right: 20px;
        width: auto;
        padding: 12px 20px;
        font-size: 15px;
        text-align: center;
    }
} 