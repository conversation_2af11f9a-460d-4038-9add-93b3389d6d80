<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ch<PERSON>n bài học - Ôn luyện Vật lí</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/style.css">
    
    <style>
        /* Home Page Specific Styles */
        .main-content {
            min-height: 100vh;
            padding: 7rem 2rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 3rem;
            animation: fadeIn 0.6s ease-out;
        }
        
        .page-header h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            margin-bottom: 1rem;
        }
        
        .page-header p {
            font-size: 1.2rem;
            color: var(--text-secondary);
        }
        
        /* Search and Filter Section */
        .search-filter-section {
            display: flex;
            gap: 1rem;
            margin-bottom: 3rem;
            flex-wrap: wrap;
            animation: fadeIn 0.8s ease-out;
        }
        
        .search-container {
            flex: 1;
            min-width: 300px;
            position: relative;
        }
        
        .search-container input {
            width: 100%;
            padding: 1rem 1rem 1rem 3.5rem;
            background: var(--glass-bg);
            border: 2px solid var(--glass-border);
            border-radius: var(--radius-full);
            color: var(--text-primary);
            font-size: 1rem;
            transition: var(--transition-fast);
        }
        
        .search-container input:focus {
            border-color: var(--neon-purple);
            box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
        }
        
        .search-container .search-icon {
            position: absolute;
            left: 1.2rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-tertiary);
            font-size: 1.2rem;
        }
        
        .filter-container {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .filter-select {
            padding: 1rem 2rem 1rem 1rem;
            background: var(--glass-bg);
            border: 2px solid var(--glass-border);
            border-radius: var(--radius-full);
            color: var(--text-primary);
            font-size: 1rem;
            cursor: pointer;
            transition: var(--transition-fast);
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23ffffff' d='M6 9L1 4h10z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 1rem center;
            background-size: 12px;
        }
        
        .filter-select:hover,
        .filter-select:focus {
            border-color: var(--neon-purple);
            box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
            outline: none;
        }
        
        /* Tags Container */
        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 1rem;
            animation: fadeIn 1s ease-out;
        }
        
        .tag {
            padding: 0.5rem 1rem;
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-full);
            font-size: 0.9rem;
            cursor: pointer;
            transition: var(--transition-fast);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .tag:hover {
            background: var(--primary-gradient);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(168, 85, 247, 0.3);
        }
        
        .tag.active {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 5px 15px rgba(168, 85, 247, 0.3);
        }
        
        /* Lessons Grid */
        .lessons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
            animation: fadeIn 1.2s ease-out;
        }
        
        .lesson-card {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-xl);
            overflow: hidden;
            transition: var(--transition-normal);
            cursor: pointer;
            position: relative;
            backdrop-filter: blur(10px);
        }
        
        .lesson-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: linear-gradient(135deg, transparent 0%, rgba(168, 85, 247, 0.1) 100%);
            opacity: 0;
            transition: opacity var(--transition-normal);
        }
        
        .lesson-card:hover {
            transform: translateY(-10px) scale(1.02);
            border-color: var(--neon-purple);
            box-shadow: 0 20px 40px rgba(168, 85, 247, 0.3);
        }
        
        .lesson-card:hover::before {
            opacity: 1;
        }
        
        .lesson-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            transition: var(--transition-slow);
        }
        
        .lesson-card:hover .lesson-image {
            transform: scale(1.1);
        }
        
        .lesson-content {
            padding: 1.5rem;
            position: relative;
            z-index: 1;
        }
        
        .lesson-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 1rem;
        }
        
        .lesson-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .lesson-meta {
            display: flex;
            gap: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .lesson-meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .lesson-meta-item i {
            color: var(--neon-purple);
        }
        
        .lesson-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .lesson-tag {
            padding: 0.25rem 0.75rem;
            background: rgba(168, 85, 247, 0.1);
            border: 1px solid rgba(168, 85, 247, 0.3);
            border-radius: var(--radius-full);
            font-size: 0.8rem;
            color: var(--neon-purple);
        }
        
        .lesson-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .lesson-button {
            flex: 1;
            padding: 0.75rem 1.5rem;
            background: var(--primary-gradient);
            color: white;
            border: none;
            border-radius: var(--radius-full);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-fast);
            text-align: center;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .lesson-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(168, 85, 247, 0.4);
        }
        
        .lesson-button.secondary {
            background: transparent;
            border: 2px solid var(--glass-border);
            color: var(--text-primary);
        }
        
        .lesson-button.secondary:hover {
            border-color: var(--neon-purple);
            color: var(--neon-purple);
            box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
        }
        
        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 5rem;
            color: var(--text-tertiary);
            margin-bottom: 1rem;
        }
        
        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }
        
        /* Home Button Dropdown */
        .home-button-container {
            position: fixed;
            top: 2rem;
            left: 2rem;
            z-index: 1001;
        }
        
        .home-dropdown {
            position: absolute;
            top: 70px;
            left: 0;
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-lg);
            padding: 0.5rem;
            min-width: 200px;
            backdrop-filter: blur(20px);
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: var(--transition-fast);
        }
        
        .home-button-container:hover .home-dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .dropdown-item {
            padding: 0.75rem 1rem;
            border-radius: var(--radius-md);
            transition: var(--transition-fast);
        }
        
        .dropdown-item:hover {
            background: rgba(168, 85, 247, 0.1);
        }
        
        .dropdown-item a {
            color: var(--text-primary);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .dropdown-item a:hover {
            color: var(--neon-purple);
        }
        
        /* Modal Styles */
        .modal {
            display: none;
        }
        
        .modal.active {
            display: flex;
        }
        
        /* Loading State */
        .loading-skeleton {
            animation: shimmer 2s infinite;
            background: linear-gradient(90deg, var(--glass-bg) 0%, rgba(168, 85, 247, 0.1) 50%, var(--glass-bg) 100%);
            background-size: 200% 100%;
            border-radius: var(--radius-lg);
            height: 300px;
        }
        
        @keyframes shimmer {
            0% { background-position: -200% center; }
            100% { background-position: 200% center; }
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .main-content {
                padding: 5rem 1rem 2rem;
            }
            
            .lessons-grid {
                grid-template-columns: 1fr;
            }
            
            .search-filter-section {
                flex-direction: column;
            }
            
            .filter-container {
                width: 100%;
                justify-content: space-between;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Indicator -->
    <div id="loading-indicator" class="loading-indicator">
        <div class="spinner"></div>
        <p>Đang tải bài học...</p>
    </div>

    <!-- Background Animation -->
    <canvas id="network-canvas"></canvas>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Home Button with Dropdown -->
        <div class="home-button-container">
            <a href="/" class="home-button">
                <img src="https://styles.redditmedia.com/t5_851o4i/styles/profileIcon_0elfudeu2s5b1.jpg?width=256&height=256&frame=1&auto=webp&crop=256:256,smart&s=86be605407a08efe2894a6bacd089074aca51879" alt="Home">
            </a>
            <div class="home-dropdown">
                <div class="dropdown-item">
                    <a href="/admin/login">
                        <i class="fas fa-user-shield"></i>
                        <span>Vào chế độ chỉnh sửa</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Page Header -->
        <div class="page-header">
            <h1>Chọn bài học</h1>
            <p>Khám phá kho tàng kiến thức Vật lí 12 🚀</p>
        </div>
        
        <!-- Search and Filter Section -->
        <div class="search-filter-section">
            <div class="search-container">
                <i class="fas fa-search search-icon"></i>
                <input type="text" id="search-input" placeholder="Tìm kiếm bài học hoặc tag...">
            </div>
            <div class="filter-container">
                <select id="sort-select" class="filter-select">
                    <option value="newest">🆕 Mới nhất</option>
                    <option value="oldest">📅 Cũ nhất</option>
                    <option value="az">🔤 Tên A-Z</option>
                    <option value="za">🔤 Tên Z-A</option>
                    <option value="popular">🔥 Phổ biến</option>
                </select>
            </div>
        </div>
        
        <!-- Tags Container -->
        <div class="tags-container"></div>
        
        <!-- Lessons Grid -->
        <div id="lessons" class="lessons-grid">
            <!-- Lessons will be dynamically loaded here -->
        </div>

        <!-- Student Info Modal -->
        <div id="user-info-modal" class="modal">
            <div class="modal-content">
                <div class="modal-icon" style="text-align: center; font-size: 4rem; margin-bottom: 1.5rem;">
                    <i class="fas fa-user-graduate"></i>
                </div>
                <h2 style="text-align: center; margin-bottom: 2rem;">Thông tin học sinh</h2>
                <form id="user-info-form" autocomplete="off">
                    <div class="form-group">
                        <label>Họ và tên *</label>
                        <input type="text" id="student-name" required>
                    </div>
                    <div class="form-group">
                        <label>Ngày sinh</label>
                        <input type="date" id="student-dob">
                    </div>
                    <div class="form-group">
                        <label>Mã số học sinh</label>
                        <input type="text" id="student-id">
                    </div>
                    <div class="modal-buttons" style="display: flex; gap: 1rem; margin-top: 2rem;">
                        <button type="submit" class="button primary" style="flex: 1;">
                            <i class="fas fa-play"></i>
                            Làm bài
                        </button>
                        <button type="button" onclick="closeModal()" class="button secondary" style="flex: 1;">
                            <i class="fas fa-times"></i>
                            Hủy
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="/js/network-animation.js"></script>
    <script src="/js/lessons.js"></script>
    <script>
        // Add hover effect for cards
        document.addEventListener('DOMContentLoaded', () => {
            // Add stagger animation to lesson cards
            const observer = new IntersectionObserver(entries => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, index * 100);
                    }
                });
            });
            
            // Observe all lesson cards
            setTimeout(() => {
                document.querySelectorAll('.lesson-card').forEach(card => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.6s ease-out';
                    observer.observe(card);
                });
            }, 100);
        });
    </script>
</body>
</html>