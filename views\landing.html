<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ôn luyện Vật lí 12 - Next Gen Learning</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/style.css">
    
    <style>
        /* Landing Page Specific Styles */
        .hero-section {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
            padding: 2rem;
        }
        
        .hero-content {
            text-align: center;
            z-index: 2;
            max-width: 1200px;
            width: 100%;
            animation: fadeIn 1s ease-out;
        }
        
        .hero-title {
            font-size: clamp(3rem, 8vw, 6rem);
            font-weight: 900;
            margin-bottom: 1.5rem;
            line-height: 1.1;
            position: relative;
        }
        
        .hero-title span {
            display: inline-block;
            position: relative;
            animation: titleFloat 3s ease-in-out infinite;
        }
        
        .hero-title span:nth-child(1) { animation-delay: 0s; }
        .hero-title span:nth-child(2) { animation-delay: 0.1s; }
        .hero-title span:nth-child(3) { animation-delay: 0.2s; }
        .hero-title span:nth-child(4) { animation-delay: 0.3s; }
        .hero-title span:nth-child(5) { animation-delay: 0.4s; }
        
        @keyframes titleFloat {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
        
        .hero-subtitle {
            font-size: clamp(1.2rem, 3vw, 1.8rem);
            color: var(--text-secondary);
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            animation: fadeIn 1.2s ease-out;
        }
        
        .hero-cta {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            animation: fadeIn 1.4s ease-out;
        }
        
        .hero-cta .button {
            padding: 1rem 2.5rem;
            font-size: 1.1rem;
            position: relative;
            overflow: hidden;
        }
        
        .hero-cta .button.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }
        
        .hero-cta .button.primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6);
        }
        
        /* Floating Elements */
        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 15s infinite ease-in-out;
        }
        
        .floating-element:nth-child(1) {
            top: 20%;
            left: 10%;
            font-size: 4rem;
            animation-delay: 0s;
        }
        
        .floating-element:nth-child(2) {
            top: 60%;
            right: 15%;
            font-size: 3rem;
            animation-delay: 3s;
        }
        
        .floating-element:nth-child(3) {
            bottom: 20%;
            left: 20%;
            font-size: 5rem;
            animation-delay: 6s;
        }
        
        @keyframes float {
            0%, 100% { 
                transform: translateY(0) rotate(0deg); 
                opacity: 0.1;
            }
            25% { 
                transform: translateY(-20px) rotate(5deg); 
                opacity: 0.15;
            }
            50% { 
                transform: translateY(0) rotate(-5deg); 
                opacity: 0.1;
            }
            75% { 
                transform: translateY(20px) rotate(5deg); 
                opacity: 0.15;
            }
        }
        
        /* Navigation Bar */
        .nav-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            padding: 1rem 2rem;
            background: rgba(10, 10, 15, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            z-index: 1000;
            transition: var(--transition-normal);
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            justify-content: center;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .nav-bar a {
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius-full);
            transition: var(--transition-fast);
            position: relative;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .nav-bar a:hover {
            background: var(--glass-bg);
            color: var(--neon-purple);
            transform: translateY(-2px);
        }
        
        .nav-bar a svg {
            width: 20px;
            height: 20px;
        }
        
        /* Features Section */
        .features-section {
            padding: 5rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .feature-card {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-xl);
            padding: 2.5rem;
            text-align: center;
            backdrop-filter: blur(10px);
            transition: var(--transition-normal);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform var(--transition-normal);
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            border-color: var(--neon-purple);
            box-shadow: 0 20px 40px rgba(168, 85, 247, 0.3);
        }
        
        .feature-card:hover::before {
            transform: scaleX(1);
        }
        
        .feature-icon {
            font-size: 3.5rem;
            margin-bottom: 1.5rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: inline-block;
            animation: pulse 2s infinite;
        }
        
        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }
        
        .feature-card p {
            color: var(--text-secondary);
            line-height: 1.8;
        }
        
        /* Showcase Section */
        .showcase-section {
            padding: 5rem 2rem;
            background: rgba(255, 255, 255, 0.02);
            border-top: 1px solid var(--glass-border);
            border-bottom: 1px solid var(--glass-border);
        }
        
        .showcase-title {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 3rem;
        }
        
        .showcase-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .showcase-item {
            position: relative;
            border-radius: var(--radius-lg);
            overflow: hidden;
            height: 300px;
            cursor: pointer;
            transition: var(--transition-normal);
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
        }
        
        .showcase-item:hover {
            transform: scale(1.05);
            box-shadow: 0 20px 40px rgba(168, 85, 247, 0.3);
        }
        
        .showcase-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition-slow);
        }
        
        .showcase-item:hover .showcase-image {
            transform: scale(1.1);
            filter: brightness(0.7);
        }
        
        .showcase-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 2rem;
            background: linear-gradient(to top, rgba(0,0,0,0.9), transparent);
            transform: translateY(100%);
            transition: var(--transition-normal);
        }
        
        .showcase-item:hover .showcase-overlay {
            transform: translateY(0);
        }
        
        .showcase-overlay h3 {
            color: white;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }
        
        /* Modal Styles */
        .modal-content {
            background: var(--bg-secondary);
            border: 1px solid var(--glass-border);
            animation: modalSlideIn 0.3s ease-out;
        }
        
        @keyframes modalSlideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        .modal-icon {
            font-size: 4rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1.5rem;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: clamp(2.5rem, 10vw, 4rem);
            }
            
            .nav-links {
                gap: 0.5rem;
                flex-wrap: wrap;
            }
            
            .nav-bar a {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }
            
            .features-grid,
            .showcase-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="nav-bar">
        <div class="nav-links">
            <a href="/gallery" class="nav-link">
                <i class="fas fa-book"></i>
                <span>Bài học</span>
            </a>
            <a href="/lessons" class="nav-link">
                <i class="fas fa-tasks"></i>
                <span>Luyện tập</span>
            </a>
            <a href="/quizgame" class="nav-link">
                <i class="fas fa-trophy"></i>
                <span>Chinh phục</span>
            </a>
            <a href="/leaderboard" class="nav-link">
                <i class="fas fa-chart-line"></i>
                <span>Xếp hạng</span>
            </a>
        </div>
    </nav>
    
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="floating-elements">
            <div class="floating-element">⚛️</div>
            <div class="floating-element">🧪</div>
            <div class="floating-element">🔬</div>
            <div class="floating-element">📐</div>
            <div class="floating-element">💡</div>
        </div>
        
        <div class="hero-content">
            <h1 class="hero-title">
                <span>Ôn</span>
                <span>luyện</span>
                <span>Vật</span>
                <span>lí</span>
                <span>12</span>
            </h1>
            <p class="hero-subtitle">
                Học tập thông minh với AI, chinh phục mọi thử thách vật lý 🚀
            </p>
            <div class="hero-cta">
                <a href="/lessons" class="button primary">
                    <i class="fas fa-rocket"></i>
                    Bắt đầu ngay
                </a>
                <a href="/gallery" class="button secondary">
                    <i class="fas fa-book-open"></i>
                    Xem bài học
                </a>
            </div>
        </div>
    </section>
    
    <!-- Features Section -->
    <section class="features-section">
        <h2 class="text-center">Tính năng nổi bật</h2>
        <div class="features-grid">
            <div class="feature-card" onclick="window.location.href='/gallery'">
                <div class="feature-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h3>Bài học chi tiết</h3>
                <p>Nắm vững kiến thức với hệ thống bài giảng được thiết kế khoa học, dễ hiểu</p>
            </div>
            <div class="feature-card" onclick="window.location.href='/lessons'">
                <div class="feature-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <h3>Luyện tập thông minh</h3>
                <p>AI phân tích và đưa ra bài tập phù hợp với trình độ của bạn</p>
            </div>
            <div class="feature-card" onclick="window.location.href='/quizgame'">
                <div class="feature-icon">
                    <i class="fas fa-gamepad"></i>
                </div>
                <h3>Game học tập</h3>
                <p>Học mà chơi, chơi mà học với các thử thách hấp dẫn</p>
            </div>
        </div>
    </section>
    
    <!-- Showcase Section -->
    <section class="showcase-section">
        <h2 class="showcase-title">Khám phá bài học</h2>
        <div class="showcase-grid">
            <div class="showcase-item">
                <img src="/images/lesson1.jpg" alt="Nhiệt học" class="showcase-image" onerror="this.src='data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'400\' height=\'300\' viewBox=\'0 0 400 300\'%3E%3Crect width=\'400\' height=\'300\' fill=\'%23667eea\'/%3E%3Ctext x=\'50%25\' y=\'50%25\' dominant-baseline=\'middle\' text-anchor=\'middle\' font-family=\'Arial\' font-size=\'24\' fill=\'white\'%3ENhiệt học%3C/text%3E%3C/svg%3E'">
                <div class="showcase-overlay">
                    <h3>Nhiệt học</h3>
                </div>
            </div>
            <div class="showcase-item">
                <img src="/images/lesson2.jpg" alt="Khí lý tưởng" class="showcase-image" onerror="this.src='data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'400\' height=\'300\' viewBox=\'0 0 400 300\'%3E%3Crect width=\'400\' height=\'300\' fill=\'%23764ba2\'/%3E%3Ctext x=\'50%25\' y=\'50%25\' dominant-baseline=\'middle\' text-anchor=\'middle\' font-family=\'Arial\' font-size=\'24\' fill=\'white\'%3EKhí lý tưởng%3C/text%3E%3C/svg%3E'">
                <div class="showcase-overlay">
                    <h3>Khí lý tưởng</h3>
                </div>
            </div>
            <div class="showcase-item">
                <img src="/images/lesson3.jpg" alt="Từ trường" class="showcase-image" onerror="this.src='data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'400\' height=\'300\' viewBox=\'0 0 400 300\'%3E%3Crect width=\'400\' height=\'300\' fill=\'%23f093fb\'/%3E%3Ctext x=\'50%25\' y=\'50%25\' dominant-baseline=\'middle\' text-anchor=\'middle\' font-family=\'Arial\' font-size=\'24\' fill=\'white\'%3ETừ trường%3C/text%3E%3C/svg%3E'">
                <div class="showcase-overlay">
                    <h3>Từ trường</h3>
                </div>
            </div>
            <div class="showcase-item">
                <img src="/images/lesson4.jpg" alt="Vật lý hạt nhân" class="showcase-image" onerror="this.src='data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'400\' height=\'300\' viewBox=\'0 0 400 300\'%3E%3Crect width=\'400\' height=\'300\' fill=\'%23f5576c\'/%3E%3Ctext x=\'50%25\' y=\'50%25\' dominant-baseline=\'middle\' text-anchor=\'middle\' font-family=\'Arial\' font-size=\'24\' fill=\'white\'%3EVật lý hạt nhân%3C/text%3E%3C/svg%3E'">
                <div class="showcase-overlay">
                    <h3>Vật lý hạt nhân</h3>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Student Info Modal -->
    <div id="user-info-modal" class="modal">
        <div class="modal-content">
            <div class="modal-icon">
                <i class="fas fa-user-graduate"></i>
            </div>
            <h2>Thông tin học sinh</h2>
            <form id="user-info-form" autocomplete="off">
                <div class="form-group">
                    <label>Họ và tên *</label>
                    <input type="text" id="student-name" required>
                </div>
                <div class="form-group">
                    <label>Ngày sinh</label>
                    <input type="date" id="student-dob">
                </div>
                <div class="form-group">
                    <label>Mã số học sinh</label>
                    <input type="text" id="student-id">
                </div>
                <div class="form-note">
                    <p style="color: var(--text-secondary);">
                        <i class="fas fa-info-circle"></i>
                        Thông tin của bạn sẽ được sử dụng để theo dõi quá trình học tập
                    </p>
                </div>
                <div class="modal-buttons" style="display: flex; gap: 1rem; margin-top: 2rem;">
                    <button type="submit" class="button primary" style="flex: 1;">
                        <i class="fas fa-check"></i>
                        Bắt đầu
                    </button>
                    <button type="button" onclick="closeModal()" class="button secondary" style="flex: 1;">
                        <i class="fas fa-times"></i>
                        Hủy
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="/js/network-animation.js"></script>
    <script src="/js/landing.js"></script>
    <script>
        // Add smooth scroll
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
        
        // Add parallax effect on scroll
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.hero-section');
            parallax.style.transform = `translateY(${scrolled * 0.5}px)`;
        });
    </script>
</body>
</html>