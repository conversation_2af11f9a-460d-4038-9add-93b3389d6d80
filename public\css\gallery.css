.main-content {
    position: relative;
    z-index: 1;
    height: 100vh;
    display: flex;
    align-items: center;
    padding: 0 20px;
    box-sizing: border-box;
}

.gallery-container {
    max-width: 1600px;
    width: 100%;
    height: 90vh;
    margin: 0 auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
}

.gallery-layout {
    display: flex;
    gap: 0;
    height: 100%;
}

.gallery-main {
    flex: 1;
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    border-radius: 10px 0 0 10px;
    overflow: hidden;
    border-right: 2px solid rgba(0, 0, 0, 0.1);
}

.gallery-preview {
    width: 120px;
    height: 100%;
    background: rgba(0, 0, 0, 0.03);
    border-radius: 0 10px 10px 0;
    overflow-y: auto;
    scrollbar-width: thin;
}

.preview-strip {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px;
}

.preview-strip img {
    width: 100px;
    height: 150px;
    object-fit: cover;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.preview-strip img:hover {
    opacity: 1;
    transform: scale(1.05);
}

.preview-strip img.active {
    opacity: 1;
    box-shadow: 0 0 0 2px #007bff;
}

.gallery-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    overflow: hidden;
    position: relative;
    background: rgba(0, 0, 0, 0.05);
}

.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    color: #666;
    text-align: center;
}

.error-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 18px;
    color: #d9534f;
    text-align: center;
    background: rgba(255, 255, 255, 0.8);
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    max-width: 80%;
}

.gallery-content img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
    cursor: zoom-in;
}

.gallery-content img:hover {
    transform: scale(1.05);
}

.gallery-content.loading::after {
    content: 'Loading...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    color: #666;
}

.image-counter {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 14px;
    z-index: 2;
}

.gallery-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 24px;
    cursor: pointer;
    z-index: 2;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-arrow:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: translateY(-50%) scale(1.1);
}

.prev-arrow {
    left: 20px;
}

.next-arrow {
    right: 20px;
}

.navigation-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.nav-button {
    padding: 12px 24px;
    background-color: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.nav-button:hover {
    background-color: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.image-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.image-modal.active {
    display: flex;
    animation: modalFadeIn 0.3s ease;
}

.modal-image {
    max-width: 90%;
    max-height: 90vh;
    object-fit: contain;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
    transform: scale(0.9);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.image-modal.active .modal-image {
    transform: scale(1);
    opacity: 1;
}

.close-modal {
    position: absolute;
    top: 20px;
    right: 30px;
    color: white;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
    transition: all 0.3s ease;
    opacity: 0.8;
}

.close-modal:hover {
    color: #fff;
    opacity: 1;
    transform: scale(1.1);
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Mobile Responsive Styles */
@media screen and (max-width: 768px) {
    .main-content {
        padding: 10px;
    }

    .gallery-container {
        height: 85vh;
        margin: 0;
        padding: 10px;
    }

    .gallery-layout {
        flex-direction: column;
        height: 100%;
        gap: 10px;
    }

    .gallery-main {
        flex: 1;
        min-height: 0;
        border-radius: 10px;
    }

    .gallery-preview {
        width: 100%;
        height: 100px;
        border-radius: 10px;
        overflow-x: auto;
        overflow-y: hidden;
    }

    .preview-strip {
        flex-direction: row;
        height: 90px;
        gap: 10px;
        padding: 5px;
    }

    .preview-strip img {
        width: 120px;
        height: 80px;
    }
} 