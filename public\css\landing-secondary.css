/* Non-critical styles from landing.html moved here for async loading */

.nav-bar a svg {
    width: 20px;
    height: 20px;
    stroke: currentColor;
    stroke-width: 2;
    transition: all 0.3s ease;
}

.nav-bar a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(46, 204, 113, 0.1) 100%);
    transform: translateY(100%);
    transition: transform 0.3s ease;
    z-index: -1;
}

.nav-bar a:hover {
    color: #3498db;
    transform: translateY(-2px);
}

.nav-bar a:hover svg {
    transform: scale(1.1);
    stroke: #3498db;
}

.nav-bar a:hover::before {
    transform: translateY(0);
}

.hero-title span {
    display: inline-block;
    opacity: 0;
    animation: fadeInSlideUp 0.5s ease forwards;
}

.hero-title span:nth-child(1) { animation-delay: 0.7s; }
.hero-title span:nth-child(2) { animation-delay: 0.8s; }
.hero-title span:nth-child(3) { animation-delay: 1s; }
.hero-title span:nth-child(4) { animation-delay: 1.1s; }
.hero-title span:nth-child(5) { animation-delay: 1.2s; }

.interactive-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
}

.graph-container {
    position: absolute;
    bottom: 15%;
    left: 0;
    width: 100%;
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.graph-line {
    position: absolute;
    width: 80%;
    height: 2px;
    background: rgba(52, 152, 219, 0.2);
    overflow: visible;
}

.graph-point {
    position: absolute;
    width: 12px;
    height: 12px;
    background: #3498db;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
}

.graph-point::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(52, 152, 219, 0.3);
    border-radius: 50%;
    animation: pulse 1s ease-out infinite;
}

.graph-line::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(52, 152, 219, 0.2) 50%, 
        transparent 100%);
    animation: shimmer 2s linear infinite;
}

/* Features section */
.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
    padding: 6rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
}

.feature-card {
    background: white;
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInSlideUp 0.8s ease forwards;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    min-height: 320px;
}

.feature-card:nth-child(1) { animation-delay: 1.6s; }
.feature-card:nth-child(2) { animation-delay: 1.8s; }
.feature-card:nth-child(3) { animation-delay: 2s; }

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.12);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

/* Showcase section */
.showcase-section {
    padding: 6rem 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.showcase-title {
    text-align: center;
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 3rem;
    font-weight: 700;
    opacity: 0;
    animation: fadeInSlideUp 0.8s ease forwards;
    animation-delay: 0.5s;
}

.showcase-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

.showcase-item {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInSlideUp 0.8s ease forwards;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal.active {
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 1;
}

.modal-content {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 450px;
    padding: 25px;
    position: relative;
    transition: transform 0.3s ease;
    transform: scale(0.9);
}

.modal.active .modal-content {
    transform: scale(1);
}

.modal-icon {
    text-align: center;
    margin-bottom: 15px;
    color: #3498db;
}

.modal h2 {
    text-align: center;
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
    font-size: 1.5em;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #555;
    font-weight: 600;
    font-size: 0.9em;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 8px;
    outline: none;
    transition: border-color 0.3s;
    font-size: 1em;
    box-sizing: border-box;
}

.form-group input:focus {
    border-color: #3498db;
}

.form-note {
    font-size: 0.8em;
    color: #777;
    margin: 15px 0;
}

.modal-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

.animated-button {
    background: linear-gradient(135deg, #3498db, #2ecc71);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s, box-shadow 0.3s;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.animated-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.cancel-button {
    background: transparent;
    color: #555;
    border: 1px solid #ddd;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
}

.cancel-button:hover {
    background: #f5f5f5;
}

/* Additional animation keyframes */
@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    100% { transform: scale(3); opacity: 0; }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Media queries for smaller screens */
@media (max-width: 768px) {
    .nav-bar {
        padding: 0.8rem 1rem;
    }

    .nav-links {
        gap: 1rem;
    }

    .nav-bar a {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }

    .hero-subtitle {
        font-size: 1rem;
        padding: 0 1rem;
    }

    .features {
        padding: 4rem 1rem;
        gap: 1.5rem;
    }

    .feature-card {
        padding: 1.5rem;
        min-height: 280px;
    }

    .showcase-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 480px) {
    .modal-content {
        width: 85%;
        padding: 1.2rem;
        margin: 15px auto;
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-icon svg {
        width: 48px;
        height: 48px;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-group label {
        font-size: 0.9rem;
    }

    .form-note {
        font-size: 0.8rem;
    }

    .form-group input {
        padding: 0.6rem;
    }
    
    .showcase-title {
        font-size: 1.8rem;
    }

    .showcase-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .showcase-image {
        height: 180px;
    }
} 