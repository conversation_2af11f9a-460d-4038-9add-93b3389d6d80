const { supabase, supabaseAdmin } = require('../config/database');

class DatabaseService {
  // Lesson operations
  async getLessons(options = {}) {
    const { page = 1, limit = 10, search = '', sort = 'order' } = options;
    const startIndex = (page - 1) * limit;

    // Determine sorting parameters
    let orderAscending = true;
    let orderColumn = 'order';
    switch (sort) {
      case 'newest': orderColumn = 'created'; orderAscending = false; break;
      case 'oldest': orderColumn = 'created'; orderAscending = true; break;
      case 'az': orderColumn = 'title'; orderAscending = true; break;
      case 'za': orderColumn = 'title'; orderAscending = false; break;
      case 'newest-changed': orderColumn = 'lastUpdated'; orderAscending = false; break;
      case 'popular': orderColumn = 'views'; orderAscending = false; break;
      case 'order': orderColumn = 'order'; orderAscending = true; break;
    }

    let lessons = [];
    let total = 0;

    if (search) {
      // Use RPC for search
      let rpcQuery = supabase
        .rpc('search_lessons', { search_term: search })
        .order(orderColumn, { ascending: orderAscending })
        .range(startIndex, startIndex + limit - 1);

      const { data: rpcData, error: rpcError } = await rpcQuery;
      if (rpcError) throw rpcError;

      lessons = rpcData || [];

      // Get total count for search results
      const { count, error: countError } = await supabase
        .rpc('search_lessons', { search_term: search }, { count: 'exact', head: true });

      if (countError) {
        console.warn('Could not get total count for search results:', countError);
        total = lessons.length + startIndex;
      } else {
        total = count || 0;
      }
    } else {
      // Regular query without search
      let query = supabase
        .from('lessons')
        .select('id, title, color, created, lastUpdated, views, order, subject, grade, tags, description, purpose, pricing, lessonImage, randomQuestions', { count: 'exact' })
        .order(orderColumn, { ascending: orderAscending })
        .range(startIndex, startIndex + limit - 1);

      const { data, error, count } = await query;
      if (error) throw error;

      lessons = data || [];
      total = count || 0;
    }

    return { lessons, total, page, limit, search, sort };
  }

  async getLessonById(id) {
    const { data: lesson, error } = await supabase
      .from('lessons')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new Error('Lesson not found');
      }
      throw error;
    }

    return lesson;
  }

  async createLesson(lessonData) {
    // Get next order number
    const { data: maxOrderLesson, error: maxOrderError } = await supabase
      .from('lessons')
      .select('order')
      .order('order', { ascending: false })
      .limit(1)
      .single();

    let nextOrder = 0;
    if (maxOrderError && maxOrderError.code !== 'PGRST116') {
      throw maxOrderError;
    }
    if (maxOrderLesson && typeof maxOrderLesson.order === 'number') {
      nextOrder = maxOrderLesson.order + 1;
    }

    const now = new Date().toISOString();
    const newLessonData = {
      ...lessonData,
      id: lessonData.id || Date.now().toString(),
      views: 0,
      lastUpdated: now,
      created: now,
      order: nextOrder
    };

    const { data, error } = await supabase
      .from('lessons')
      .insert(newLessonData)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateLesson(id, updateData) {
    const updatedData = {
      ...updateData,
      lastUpdated: new Date().toISOString()
    };
    
    // Remove fields that shouldn't be updated
    delete updatedData.id;
    delete updatedData.created;

    const { data, error } = await supabase
      .from('lessons')
      .update(updatedData)
      .eq('id', id)
      .select();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new Error('Lesson not found');
      }
      throw error;
    }

    return data;
  }

  async deleteLesson(id) {
    const { error } = await supabase
      .from('lessons')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return true;
  }

  async updateLessonOrder(orderedLessons) {
    const updates = orderedLessons.map((lesson, index) => 
      supabase
        .from('lessons')
        .update({ order: index })
        .eq('id', lesson.id)
    );

    const results = await Promise.all(updates);
    const errors = results.filter(result => result.error);
    
    if (errors.length > 0) {
      console.error('Errors updating lesson order:', errors);
      throw new Error('One or more lessons failed to update order.');
    }

    return true;
  }

  async incrementLessonViews(lessonId, currentViews) {
    const { error } = await supabase
      .from('lessons')
      .update({ views: currentViews + 1 })
      .eq('id', lessonId);

    if (error) throw error;
    return true;
  }

  // Student operations
  async getStudentByPhone(phoneNumber) {
    const { data: student, error } = await supabase
      .from('students')
      .select('id, full_name, password_hash, is_approved, approved_device_id, approved_device_fingerprint, current_session_id')
      .eq('phone_number', phoneNumber)
      .maybeSingle();

    if (error) throw error;
    return student;
  }

  async createStudent(studentData) {
    const { data: newStudent, error } = await supabase
      .from('students')
      .insert({
        ...studentData,
        is_approved: false,
        created_at: new Date().toISOString()
      })
      .select('id')
      .single();

    if (error) throw error;
    return newStudent;
  }

  async updateStudent(id, updateData) {
    const { error } = await supabase
      .from('students')
      .update(updateData)
      .eq('id', id);

    if (error) throw error;
    return true;
  }

  async getStudents(options = {}) {
    const { limit = 100, approved = null } = options;
    
    let query = supabase
      .from('students')
      .select('*')
      .order('created_at', { ascending: false });

    if (approved !== null) {
      query = query.eq('is_approved', approved);
    }

    if (limit) {
      query = query.limit(limit);
    }

    const { data: students, error } = await query;
    if (error) throw error;
    return students || [];
  }

  // Results operations
  async createResult(resultData) {
    const { data: savedResult, error } = await supabase
      .from('results')
      .insert(resultData)
      .select('id')
      .single();

    if (error) throw error;
    return savedResult;
  }

  async getResultById(id) {
    const { data: result, error } = await supabase
      .from('results')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new Error('Result not found');
      }
      throw error;
    }

    return result;
  }

  async deleteResult(id) {
    const { error } = await supabase
      .from('results')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return true;
  }

  async getLessonResults(lessonId) {
    const { data: results, error } = await supabase
      .from('results')
      .select(`
        *,
        students ( full_name )
      `)
      .eq('lessonId', lessonId);

    if (error) throw error;
    return results || [];
  }

  // Rating operations
  async getRatings(limit = 100, offset = 0) {
    const { data: ratings, error } = await supabase
      .from('ratings')
      .select(`
        *,
        students ( full_name )
      `)
      .order('rating', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;
    return ratings || [];
  }

  async getStudentRating(studentId) {
    const { data: rating, error } = await supabase
      .from('ratings')
      .select(`
        *,
        students ( full_name )
      `)
      .eq('student_id', studentId)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return rating;
  }

  async upsertRating(ratingData) {
    const { error } = await supabase
      .from('ratings')
      .upsert(ratingData);

    if (error) throw error;
    return true;
  }

  async createRatingHistory(historyData) {
    const { error } = await supabase
      .from('rating_history')
      .insert(historyData);

    if (error) throw error;
    return true;
  }

  async getStudentRatingHistory(studentId, limit = 50) {
    const { data: history, error } = await supabase
      .from('rating_history')
      .select('*')
      .eq('student_id', studentId)
      .order('timestamp', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return history || [];
  }

  async getStudentProfile(studentId) {
    // Get student info
    const { data: student, error: studentError } = await supabase
      .from('students')
      .select('id, full_name, created_at')
      .eq('id', studentId)
      .maybeSingle();

    if (studentError) throw studentError;
    if (!student) throw new Error('Student not found');

    // Get current rating
    const { data: rating, error: ratingError } = await supabase
      .from('ratings')
      .select('rating')
      .eq('student_id', studentId)
      .maybeSingle();

    if (ratingError) {
      console.warn(`Could not fetch rating for student ${studentId}:`, ratingError.message);
    }

    // Get rating history with lesson titles
    const { data: ratingHistory, error: historyError } = await supabase
      .from('rating_history')
      .select(`
        *,
        lessons ( title )
      `)
      .eq('student_id', studentId)
      .order('timestamp', { ascending: false })
      .limit(20);

    if (historyError) {
      console.error(`Error fetching rating history for student ${studentId}:`, historyError);
    }

    // Format history
    const formattedHistory = ratingHistory?.map(item => ({
      ...item,
      lesson_title: item.lessons?.title
    })) || [];

    return {
      student,
      rating,
      ratingHistory: formattedHistory
    };
  }

  // Get student by ID
  async getStudentById(studentId) {
    const { data: student, error } = await supabase
      .from('students')
      .select('*')
      .eq('id', studentId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new Error('Student not found');
      }
      throw error;
    }
    return student;
  }

  // Save raw lesson content (for session storage fallback)
  async saveRawLessonContent(id, content, userId) {
    const { data, error } = await supabaseAdmin
      .from('temp_lesson_content')
      .upsert({
        id: id,
        content: content,
        created_at: new Date().toISOString(),
        user_id: userId || 'unknown'
      });

    if (error) throw error;
    return data;
  }

  // Get raw lesson content
  async getRawLessonContent(id) {
    const { data, error } = await supabaseAdmin
      .from('temp_lesson_content')
      .select('content')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new Error('Content not found');
      }
      throw error;
    }
    return data;
  }

  // Get quiz data
  async getQuizData() {
    const { data: quizConfig, error } = await supabase
      .from('quizzes')
      .select('quiz_data')
      .eq('id', 'main_quiz')
      .maybeSingle();

    if (error) throw error;
    return quizConfig?.quiz_data || { questions: [] };
  }

  // Save quiz result
  async saveQuizResult(resultData) {
    const { data, error } = await supabase
      .from('quiz_results')
      .insert(resultData)
      .select('id')
      .single();

    if (error) throw error;
    return data;
  }

  // Save quiz data (admin)
  async saveQuizData(quizData) {
    const { error } = await supabase
      .from('quizzes')
      .upsert({
        id: 'main_quiz',
        quiz_data: quizData
      });

    if (error) throw error;
    return true;
  }

  // Get all unique tags from lessons
  async getAllUniqueTags() {
    const { data, error } = await supabase
      .from('lessons')
      .select('tags');

    if (error) throw error;

    const allTags = new Set();
    if (data) {
      data.forEach(lesson => {
        if (Array.isArray(lesson.tags)) {
          lesson.tags.forEach(tag => {
            if (tag && typeof tag === 'string') {
              allTags.add(tag.trim());
            }
          });
        }
      });
    }

    return Array.from(allTags).sort();
  }

  // Delete student and all associated data
  async deleteStudentAndData(studentId) {
    console.warn(`ADMIN ACTION: Attempting to permanently delete student ${studentId} and all related data.`);

    try {
      // Delete in order to avoid foreign key constraints

      // 1. Delete rating history
      console.log(`Deleting rating history for student ${studentId}...`);
      const { error: historyError } = await supabaseAdmin
        .from('rating_history')
        .delete()
        .eq('student_id', studentId);
      if (historyError) {
        console.error('Error deleting rating history:', historyError);
      }

      // 2. Delete ratings
      console.log(`Deleting ratings for student ${studentId}...`);
      const { error: ratingError } = await supabaseAdmin
        .from('ratings')
        .delete()
        .eq('student_id', studentId);
      if (ratingError) {
        console.error('Error deleting ratings:', ratingError);
      }

      // 3. Delete quiz results
      console.log(`Deleting quiz results for student ${studentId}...`);
      const { error: quizResultsError } = await supabaseAdmin
        .from('quiz_results')
        .delete()
        .eq('student_id', studentId);
      if (quizResultsError) {
        console.error('Error deleting quiz results:', quizResultsError);
      }

      // 4. Delete lesson results
      console.log(`Deleting lesson results for student ${studentId}...`);
      const { error: resultsError } = await supabaseAdmin
        .from('results')
        .delete()
        .eq('student_id', studentId);
      if (resultsError) {
        console.error('Error deleting lesson results:', resultsError);
      }

      // 5. Finally, delete the student record
      console.log(`Deleting student record ${studentId}...`);
      const { error: studentDeleteError } = await supabaseAdmin
        .from('students')
        .delete()
        .eq('id', studentId);

      if (studentDeleteError) {
        console.error('Critical error deleting student record:', studentDeleteError);
        throw new Error(`Failed to delete student record: ${studentDeleteError.message}`);
      }

      console.log(`Successfully deleted student ${studentId} and associated data.`);
      return true;

    } catch (error) {
      console.error(`Error processing delete request for student ${studentId}:`, error);
      throw error;
    }
  }

  // Update device information for student
  async updateDeviceInfo(studentId, deviceId, deviceFingerprint) {
    const updateData = {};

    if (deviceId) {
      // Check if this is a device_id (new system) or device_fingerprint (legacy)
      if (deviceId.length > 20) { // Assume device_id is longer
        updateData.approved_device_id = deviceId;
        updateData.device_registered_at = new Date().toISOString();
      } else {
        // Legacy fingerprint support
        updateData.approved_device_fingerprint = deviceId;
      }
    }

    if (deviceFingerprint) {
      updateData.approved_device_fingerprint = deviceFingerprint;
    }

    await this.updateStudent(studentId, updateData);
    return true;
  }

  // Unbind device from student
  async unbindDevice(studentId) {
    const { data, error } = await supabase
      .from('students')
      .update({
        approved_device_fingerprint: null,
        approved_device_id: null
      })
      .eq('id', studentId)
      .select('id')
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new Error('Student not found');
      }
      throw error;
    }

    return true;
  }

  // Get lesson results with student info
  async getLessonResultsWithStudents(lessonId) {
    const { data: results, error } = await supabase
      .from('results')
      .select(`
        *,
        students ( full_name )
      `)
      .eq('lessonId', lessonId);

    if (error) throw error;
    return results || [];
  }

  // Get history with pagination
  async getHistoryWithPagination(options = {}) {
    const { page = 1, limit = 15, search = '', sort = 'time-desc' } = options;
    const startIndex = (page - 1) * limit;

    // Determine sorting
    let orderAscending = false;
    let orderColumn = 'timestamp';

    const sortMap = {
      'time-asc': { column: 'timestamp', ascending: true },
      'time-desc': { column: 'timestamp', ascending: false },
      'score-asc': { column: 'score', ascending: true },
      'score-desc': { column: 'score', ascending: false },
    };

    if (sortMap[sort]) {
      orderColumn = sortMap[sort].column;
      orderAscending = sortMap[sort].ascending;
    }

    let query = supabase
      .from('results')
      .select(`
        id,
        student_id,
        timestamp,
        score,
        totalPoints,
        lessonId,
        students!inner ( full_name ),
        lessons ( title )
      `, { count: 'exact' });

    // Apply search filter if provided
    if (search) {
      query = query.or(`students.full_name.ilike.%${search}%,lessons.title.ilike.%${search}%`);
    }

    // Apply sorting
    query = query.order(orderColumn, { ascending: orderAscending });

    // Apply pagination
    query = query.range(startIndex, startIndex + limit - 1);

    const { data: historyData, error, count: totalCount } = await query;

    if (error) throw error;

    const history = historyData.map(result => ({
      resultId: result.id,
      studentName: result.students?.full_name || 'Unknown Student',
      lessonTitle: result.lessons?.title || (result.lessonId === 'quiz_game' ? 'Trò chơi chinh phục' : 'Unknown Lesson'),
      submittedAt: result.timestamp,
      score: result.score,
      totalPoints: result.totalPoints,
      scorePercentage: result.totalPoints ? ((result.score / result.totalPoints) * 100).toFixed(1) + '%' : 'N/A'
    }));

    return {
      history,
      total: totalCount || 0,
      page,
      limit
    };
  }
}

module.exports = new DatabaseService();
