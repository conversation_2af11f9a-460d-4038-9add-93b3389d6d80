[2025-07-02T05:04:57.697Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-02T05:04:57.697Z"}
[2025-07-02T05:05:00.720Z] [INFO] GET /css/style.css - 200 - 14ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"14ms","timestamp":"2025-07-02T05:05:00.720Z"}
[2025-07-02T05:05:00.884Z] [INFO] GET /js/network-animation.js - 200 - 3ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-02T05:05:00.884Z"}
[2025-07-02T05:05:00.887Z] [INFO] GET /js/landing.js - 200 - 5ms | {"method":"GET","url":"/js/landing.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-02T05:05:00.887Z"}
[2025-07-02T05:05:04.688Z] [INFO] GET /lessons - 302 - 6ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":302,"responseTime":"6ms","timestamp":"2025-07-02T05:05:04.688Z"}
[2025-07-02T05:05:04.695Z] [INFO] GET /student/login?redirect=%2Flessons - 200 - 2ms | {"method":"GET","url":"/student/login?redirect=%2Flessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-02T05:05:04.695Z"}
[2025-07-02T05:05:04.740Z] [INFO] GET /js/device-id.js - 200 - 4ms | {"method":"GET","url":"/js/device-id.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-02T05:05:04.740Z"}
[2025-07-02T05:05:04.743Z] [INFO] GET /css/style.css - 200 - 7ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"7ms","timestamp":"2025-07-02T05:05:04.743Z"}
[2025-07-02T05:05:07.394Z] [WARN] 404 Not Found | {"url":"/api/student/login","method":"POST","ip":"::1"}
[2025-07-02T05:05:07.401Z] [INFO] POST / - 404 - 17ms | {"method":"POST","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"17ms","timestamp":"2025-07-02T05:05:07.401Z"}
[2025-07-02T05:05:11.823Z] [INFO] GET /css/style.css - 200 - 4ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-02T05:05:11.823Z"}
[2025-07-02T05:05:11.858Z] [WARN] 404 Not Found | {"url":"/.well-known/appspecific/com.chrome.devtools.json","method":"GET","ip":"::1"}
[2025-07-02T05:05:11.863Z] [INFO] GET / - 404 - 8ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"8ms","timestamp":"2025-07-02T05:05:11.863Z"}
