[2025-07-02T03:39:40.971Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-02T03:39:40.971Z"}
[2025-07-02T03:39:52.185Z] [INFO] GET / - 200 - 18ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"18ms","timestamp":"2025-07-02T03:39:52.185Z"}
[2025-07-02T03:39:52.975Z] [WARN] 404 Not Found | {"url":"/favicon.ico","method":"GET","ip":"::1"}
[2025-07-02T03:39:52.983Z] [INFO] GET / - 404 - 11ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"11ms","timestamp":"2025-07-02T03:39:52.983Z"}
[2025-07-02T03:40:07.508Z] [INFO] GET /lessons - 200 - 3ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-02T03:40:07.508Z"}
[2025-07-02T03:40:07.815Z] [INFO] GET /check-student-auth - 200 - 4ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-02T03:40:07.815Z"}
[2025-07-02T03:40:07.955Z] [INFO] GET /student/login?redirect=%2Flessons - 200 - 2ms | {"method":"GET","url":"/student/login?redirect=%2Flessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-02T03:40:07.955Z"}
[2025-07-02T03:40:17.548Z] [INFO] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-02T03:40:17.548Z"}
[2025-07-02T03:40:21.677Z] [INFO] GET / - 200 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-02T03:40:21.677Z"}
[2025-07-02T03:40:21.720Z] [INFO] GET /images/lesson1.jpg - 200 - 6ms | {"method":"GET","url":"/images/lesson1.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-02T03:40:21.720Z"}
[2025-07-02T03:40:21.723Z] [INFO] GET /css/style.css - 200 - 10ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"10ms","timestamp":"2025-07-02T03:40:21.723Z"}
[2025-07-02T03:40:21.725Z] [INFO] GET /images/lesson2.jpg - 200 - 9ms | {"method":"GET","url":"/images/lesson2.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"9ms","timestamp":"2025-07-02T03:40:21.725Z"}
[2025-07-02T03:40:21.782Z] [INFO] GET /images/lesson3.jpg - 200 - 4ms | {"method":"GET","url":"/images/lesson3.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-02T03:40:21.782Z"}
[2025-07-02T03:40:21.785Z] [INFO] GET /images/lesson4.jpg - 200 - 5ms | {"method":"GET","url":"/images/lesson4.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-02T03:40:21.785Z"}
[2025-07-02T03:40:21.787Z] [INFO] GET /js/network-animation.js - 200 - 7ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"7ms","timestamp":"2025-07-02T03:40:21.787Z"}
[2025-07-02T03:40:21.789Z] [INFO] GET /js/landing.js - 200 - 9ms | {"method":"GET","url":"/js/landing.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"9ms","timestamp":"2025-07-02T03:40:21.789Z"}
[2025-07-02T03:40:28.016Z] [INFO] GET /lessons - 200 - 2ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-02T03:40:28.016Z"}
[2025-07-02T03:40:28.044Z] [INFO] GET /js/index.js - 200 - 2ms | {"method":"GET","url":"/js/index.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-02T03:40:28.044Z"}
[2025-07-02T03:40:28.090Z] [INFO] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-02T03:40:28.090Z"}
[2025-07-02T03:40:28.581Z] [INFO] GET / - 200 - 479ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"479ms","timestamp":"2025-07-02T03:40:28.581Z"}
[2025-07-02T03:40:28.657Z] [INFO] GET /?page=1&limit=10&sort=newest - 200 - 70ms | {"method":"GET","url":"/?page=1&limit=10&sort=newest","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"70ms","timestamp":"2025-07-02T03:40:28.657Z"}
[2025-07-02T03:40:36.732Z] [INFO] GET /lesson/1748074653639 - 200 - 1ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-02T03:40:36.732Z"}
[2025-07-02T03:40:36.756Z] [INFO] GET /css/lesson-questions.css - 200 - 1ms | {"method":"GET","url":"/css/lesson-questions.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-02T03:40:36.756Z"}
[2025-07-02T03:40:36.761Z] [INFO] GET /js/lesson.js - 200 - 3ms | {"method":"GET","url":"/js/lesson.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-02T03:40:36.761Z"}
[2025-07-02T03:40:36.932Z] [INFO] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-02T03:40:36.932Z"}
[2025-07-02T03:40:37.167Z] [INFO] GET /1748074653639 - 200 - 228ms | {"method":"GET","url":"/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"228ms","timestamp":"2025-07-02T03:40:37.167Z"}
[2025-07-02T03:40:47.672Z] [INFO] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-02T03:40:47.672Z"}
[2025-07-02T03:40:50.002Z] [INFO] GET /student/login?redirect=%2Flesson%2F1748074653639 - 200 - 2ms | {"method":"GET","url":"/student/login?redirect=%2Flesson%2F1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-02T03:40:50.002Z"}
[2025-07-02T03:40:50.034Z] [INFO] GET /js/device-id.js - 200 - 2ms | {"method":"GET","url":"/js/device-id.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-02T03:40:50.034Z"}
[2025-07-02T03:40:58.615Z] [INFO] POST /student/login - 200 - 738ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"738ms","timestamp":"2025-07-02T03:40:58.615Z"}
[2025-07-02T03:40:59.213Z] [INFO] GET /lesson/1748074653639 - 304 - 66ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"66ms","timestamp":"2025-07-02T03:40:59.213Z"}
[2025-07-02T03:40:59.324Z] [INFO] GET /check-student-auth - 200 - 66ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"66ms","timestamp":"2025-07-02T03:40:59.324Z"}
[2025-07-02T03:41:03.729Z] [INFO] GET /check-student-auth - 200 - 63ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"63ms","timestamp":"2025-07-02T03:41:03.729Z"}
[2025-07-02T03:41:05.877Z] [INFO] GET /student/login?redirect=%2Flesson%2F1748074653639 - 200 - 66ms | {"method":"GET","url":"/student/login?redirect=%2Flesson%2F1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"66ms","timestamp":"2025-07-02T03:41:05.877Z"}
[2025-07-02T03:59:56.729Z] [INFO] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-02T03:59:56.729Z"}
[2025-07-02T03:59:58.524Z] [INFO] GET /admin - 401 - 2ms | {"method":"GET","url":"/admin","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"2ms","timestamp":"2025-07-02T03:59:58.524Z"}
[2025-07-02T04:00:02.118Z] [WARN] 404 Not Found | {"url":"/admina","method":"GET","ip":"::1"}
[2025-07-02T04:00:02.120Z] [INFO] GET / - 404 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-02T04:00:02.120Z"}
[2025-07-02T04:01:13.403Z] [INFO] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-02T04:01:13.403Z"}
[2025-07-02T04:16:53.355Z] [INFO] GET / - 304 - 276ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"276ms","timestamp":"2025-07-02T04:16:53.355Z"}
[2025-07-02T04:16:54.612Z] [INFO] GET /quizgame - 200 - 62ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"62ms","timestamp":"2025-07-02T04:16:54.612Z"}
[2025-07-02T04:16:54.617Z] [INFO] GET /js/quizgame.js - 200 - 1ms | {"method":"GET","url":"/js/quizgame.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-02T04:16:54.617Z"}
[2025-07-02T04:16:54.780Z] [INFO] GET /audio/5sec_1.mp3 - 206 - 6ms | {"method":"GET","url":"/audio/5sec_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"6ms","timestamp":"2025-07-02T04:16:54.780Z"}
[2025-07-02T04:16:54.782Z] [INFO] GET /audio/30sec_1.mp3 - 206 - 14ms | {"method":"GET","url":"/audio/30sec_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"14ms","timestamp":"2025-07-02T04:16:54.782Z"}
[2025-07-02T04:16:54.786Z] [INFO] GET /audio/5sec_2.mp3 - 206 - 8ms | {"method":"GET","url":"/audio/5sec_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"8ms","timestamp":"2025-07-02T04:16:54.786Z"}
[2025-07-02T04:16:54.789Z] [INFO] GET /audio/30sec_2.mp3 - 206 - 18ms | {"method":"GET","url":"/audio/30sec_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"18ms","timestamp":"2025-07-02T04:16:54.789Z"}
[2025-07-02T04:16:54.791Z] [INFO] GET /audio/30sec_3.mp3 - 206 - 19ms | {"method":"GET","url":"/audio/30sec_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"19ms","timestamp":"2025-07-02T04:16:54.791Z"}
[2025-07-02T04:16:54.796Z] [INFO] GET /audio/5sec_3.mp3 - 206 - 16ms | {"method":"GET","url":"/audio/5sec_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"16ms","timestamp":"2025-07-02T04:16:54.796Z"}
[2025-07-02T04:16:54.799Z] [INFO] GET /audio/correct_1.mp3 - 206 - 14ms | {"method":"GET","url":"/audio/correct_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"14ms","timestamp":"2025-07-02T04:16:54.799Z"}
[2025-07-02T04:16:54.803Z] [INFO] GET /audio/correct_2.mp3 - 206 - 18ms | {"method":"GET","url":"/audio/correct_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"18ms","timestamp":"2025-07-02T04:16:54.803Z"}
[2025-07-02T04:16:54.807Z] [INFO] GET /audio/correct_3.mp3 - 206 - 12ms | {"method":"GET","url":"/audio/correct_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"12ms","timestamp":"2025-07-02T04:16:54.807Z"}
[2025-07-02T04:16:54.811Z] [INFO] GET /audio/correct_5.mp3 - 206 - 9ms | {"method":"GET","url":"/audio/correct_5.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"9ms","timestamp":"2025-07-02T04:16:54.811Z"}
[2025-07-02T04:16:54.815Z] [INFO] GET /audio/correct_4.mp3 - 206 - 14ms | {"method":"GET","url":"/audio/correct_4.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"14ms","timestamp":"2025-07-02T04:16:54.815Z"}
[2025-07-02T04:16:54.818Z] [INFO] GET /audio/incorrect.mp3 - 206 - 12ms | {"method":"GET","url":"/audio/incorrect.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"12ms","timestamp":"2025-07-02T04:16:54.818Z"}
[2025-07-02T04:16:54.820Z] [INFO] GET /audio/points.mp3 - 206 - 14ms | {"method":"GET","url":"/audio/points.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"14ms","timestamp":"2025-07-02T04:16:54.820Z"}
[2025-07-02T04:16:57.333Z] [INFO] GET /leaderboard - 200 - 65ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"65ms","timestamp":"2025-07-02T04:16:57.333Z"}
[2025-07-02T04:16:57.908Z] [INFO] GET /?page=1&filter=all - 200 - 569ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"569ms","timestamp":"2025-07-02T04:16:57.908Z"}
[2025-07-02T04:16:59.006Z] [INFO] GET /?page=1&filter=month - 200 - 139ms | {"method":"GET","url":"/?page=1&filter=month","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"139ms","timestamp":"2025-07-02T04:16:59.006Z"}
[2025-07-02T04:17:03.497Z] [WARN] 404 Not Found | {"url":"/profile/1418904f-73ee-4ee4-ad54-0c5e7a2d6f92","method":"GET","ip":"::1"}
[2025-07-02T04:17:03.530Z] [INFO] GET / - 404 - 64ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"64ms","timestamp":"2025-07-02T04:17:03.530Z"}
[2025-07-02T04:17:27.470Z] [INFO] GET / - 304 - 280ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"280ms","timestamp":"2025-07-02T04:17:27.470Z"}
[2025-07-02T04:17:28.209Z] [INFO] GET /lessons - 304 - 70ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"70ms","timestamp":"2025-07-02T04:17:28.209Z"}
[2025-07-02T04:17:28.343Z] [INFO] GET /check-student-auth - 200 - 70ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"70ms","timestamp":"2025-07-02T04:17:28.343Z"}
[2025-07-02T04:17:28.631Z] [INFO] GET /?page=1&limit=10&sort=newest - 200 - 283ms | {"method":"GET","url":"/?page=1&limit=10&sort=newest","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"283ms","timestamp":"2025-07-02T04:17:28.631Z"}
[2025-07-02T04:17:34.741Z] [INFO] GET /lesson/1748074653639 - 304 - 69ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"69ms","timestamp":"2025-07-02T04:17:34.741Z"}
[2025-07-02T04:17:34.862Z] [INFO] GET /check-student-auth - 200 - 69ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-02T04:17:34.862Z"}
[2025-07-02T04:17:35.170Z] [INFO] GET /1748074653639 - 200 - 305ms | {"method":"GET","url":"/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"305ms","timestamp":"2025-07-02T04:17:35.170Z"}
[2025-07-02T04:17:40.180Z] [INFO] GET /check-student-auth - 200 - 65ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"65ms","timestamp":"2025-07-02T04:17:40.180Z"}
[2025-07-02T04:17:42.835Z] [WARN] 404 Not Found | {"url":"/.well-known/appspecific/com.chrome.devtools.json","method":"GET","ip":"::1"}
[2025-07-02T04:17:42.870Z] [INFO] GET / - 404 - 68ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"68ms","timestamp":"2025-07-02T04:17:42.870Z"}
[2025-07-02T04:17:49.322Z] [INFO] GET / - 304 - 65ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"65ms","timestamp":"2025-07-02T04:17:49.322Z"}
[2025-07-02T04:17:50.141Z] [INFO] GET /lessons - 304 - 70ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"70ms","timestamp":"2025-07-02T04:17:50.141Z"}
[2025-07-02T04:17:50.257Z] [INFO] GET /check-student-auth - 200 - 68ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-02T04:17:50.257Z"}
[2025-07-02T04:17:52.058Z] [INFO] GET /lesson/1747897954640 - 200 - 69ms | {"method":"GET","url":"/lesson/1747897954640","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-02T04:17:52.058Z"}
[2025-07-02T04:17:52.158Z] [INFO] GET /check-student-auth - 200 - 68ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-02T04:17:52.158Z"}
[2025-07-02T04:17:52.441Z] [INFO] GET /1747897954640 - 200 - 280ms | {"method":"GET","url":"/1747897954640","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"280ms","timestamp":"2025-07-02T04:17:52.441Z"}
[2025-07-02T04:17:57.514Z] [INFO] GET /check-student-auth - 200 - 68ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-02T04:17:57.514Z"}
[2025-07-02T04:18:31.371Z] [INFO] GET / - 304 - 271ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"271ms","timestamp":"2025-07-02T04:18:31.371Z"}
[2025-07-02T04:18:32.755Z] [INFO] GET /lessons - 304 - 67ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"67ms","timestamp":"2025-07-02T04:18:32.755Z"}
[2025-07-02T04:18:32.871Z] [INFO] GET /check-student-auth - 200 - 72ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"72ms","timestamp":"2025-07-02T04:18:32.871Z"}
[2025-07-02T04:18:33.609Z] [INFO] GET / - 304 - 69ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"69ms","timestamp":"2025-07-02T04:18:33.609Z"}
[2025-07-02T04:18:34.267Z] [INFO] GET /js/gallery.js - 200 - 1ms | {"method":"GET","url":"/js/gallery.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-02T04:18:34.267Z"}
[2025-07-02T04:18:34.270Z] [INFO] GET /gallery - 200 - 69ms | {"method":"GET","url":"/gallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-02T04:18:34.270Z"}
[2025-07-02T04:18:34.272Z] [INFO] GET /css/gallery.css - 200 - 7ms | {"method":"GET","url":"/css/gallery.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"7ms","timestamp":"2025-07-02T04:18:34.272Z"}
[2025-07-02T04:18:34.353Z] [INFO] GET /check-student-auth - 200 - 67ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-02T04:18:34.353Z"}
[2025-07-02T04:18:34.433Z] [INFO] GET / - 200 - 75ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"75ms","timestamp":"2025-07-02T04:18:34.433Z"}
[2025-07-02T04:18:34.482Z] [INFO] GET /lesson_images/1.jpg - 200 - 37ms | {"method":"GET","url":"/lesson_images/1.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"37ms","timestamp":"2025-07-02T04:18:34.482Z"}
[2025-07-02T04:18:34.522Z] [INFO] GET /lesson_images/14.jpg - 200 - 70ms | {"method":"GET","url":"/lesson_images/14.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"70ms","timestamp":"2025-07-02T04:18:34.522Z"}
[2025-07-02T04:18:34.543Z] [INFO] GET /lesson_images/13.jpg - 200 - 90ms | {"method":"GET","url":"/lesson_images/13.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"90ms","timestamp":"2025-07-02T04:18:34.543Z"}
[2025-07-02T04:18:34.587Z] [INFO] GET /lesson_images/11.jpg - 200 - 137ms | {"method":"GET","url":"/lesson_images/11.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"137ms","timestamp":"2025-07-02T04:18:34.587Z"}
[2025-07-02T04:18:34.603Z] [INFO] GET /lesson_images/10.jpg - 200 - 156ms | {"method":"GET","url":"/lesson_images/10.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"156ms","timestamp":"2025-07-02T04:18:34.603Z"}
[2025-07-02T04:18:34.619Z] [INFO] GET /lesson_images/12.jpg - 200 - 164ms | {"method":"GET","url":"/lesson_images/12.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"164ms","timestamp":"2025-07-02T04:18:34.619Z"}
[2025-07-02T04:18:34.649Z] [INFO] GET /lesson_images/18.jpg - 200 - 18ms | {"method":"GET","url":"/lesson_images/18.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"18ms","timestamp":"2025-07-02T04:18:34.649Z"}
[2025-07-02T04:18:34.651Z] [INFO] GET /lesson_images/16.jpg - 200 - 75ms | {"method":"GET","url":"/lesson_images/16.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"75ms","timestamp":"2025-07-02T04:18:34.651Z"}
[2025-07-02T04:18:34.652Z] [INFO] GET /lesson_images/17.jpg - 200 - 45ms | {"method":"GET","url":"/lesson_images/17.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"45ms","timestamp":"2025-07-02T04:18:34.652Z"}
[2025-07-02T04:18:34.655Z] [INFO] GET /lesson_images/2.jpg - 200 - 22ms | {"method":"GET","url":"/lesson_images/2.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"22ms","timestamp":"2025-07-02T04:18:34.655Z"}
[2025-07-02T04:18:34.658Z] [INFO] GET /lesson_images/19.jpg - 200 - 26ms | {"method":"GET","url":"/lesson_images/19.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"26ms","timestamp":"2025-07-02T04:18:34.658Z"}
[2025-07-02T04:18:34.660Z] [INFO] GET /lesson_images/15.jpg - 200 - 157ms | {"method":"GET","url":"/lesson_images/15.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"157ms","timestamp":"2025-07-02T04:18:34.660Z"}
[2025-07-02T04:18:34.702Z] [INFO] GET /lesson_images/22.jpg - 200 - 34ms | {"method":"GET","url":"/lesson_images/22.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"34ms","timestamp":"2025-07-02T04:18:34.702Z"}
[2025-07-02T04:18:34.712Z] [INFO] GET /lesson_images/5.jpg - 200 - 42ms | {"method":"GET","url":"/lesson_images/5.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"42ms","timestamp":"2025-07-02T04:18:34.712Z"}
[2025-07-02T04:18:34.716Z] [INFO] GET /lesson_images/21.jpg - 200 - 49ms | {"method":"GET","url":"/lesson_images/21.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"49ms","timestamp":"2025-07-02T04:18:34.716Z"}
[2025-07-02T04:18:34.718Z] [INFO] GET /lesson_images/3.jpg - 200 - 50ms | {"method":"GET","url":"/lesson_images/3.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"50ms","timestamp":"2025-07-02T04:18:34.718Z"}
[2025-07-02T04:18:34.724Z] [INFO] GET /lesson_images/4.jpg - 200 - 55ms | {"method":"GET","url":"/lesson_images/4.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"55ms","timestamp":"2025-07-02T04:18:34.724Z"}
[2025-07-02T04:18:34.729Z] [INFO] GET /lesson_images/20.jpg - 200 - 63ms | {"method":"GET","url":"/lesson_images/20.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"63ms","timestamp":"2025-07-02T04:18:34.729Z"}
[2025-07-02T04:18:34.745Z] [INFO] GET /lesson_images/6.jpg - 200 - 30ms | {"method":"GET","url":"/lesson_images/6.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"30ms","timestamp":"2025-07-02T04:18:34.745Z"}
[2025-07-02T04:18:34.748Z] [INFO] GET /lesson_images/8.jpg - 200 - 15ms | {"method":"GET","url":"/lesson_images/8.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"15ms","timestamp":"2025-07-02T04:18:34.748Z"}
[2025-07-02T04:18:34.753Z] [INFO] GET /lesson_images/9.jpg - 200 - 19ms | {"method":"GET","url":"/lesson_images/9.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"19ms","timestamp":"2025-07-02T04:18:34.753Z"}
[2025-07-02T04:18:34.757Z] [INFO] GET /lesson_images/7.jpg - 200 - 31ms | {"method":"GET","url":"/lesson_images/7.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"31ms","timestamp":"2025-07-02T04:18:34.757Z"}
[2025-07-02T04:18:45.312Z] [INFO] GET / - 304 - 282ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"282ms","timestamp":"2025-07-02T04:18:45.312Z"}
[2025-07-02T04:18:46.474Z] [INFO] GET /lessons - 304 - 73ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"73ms","timestamp":"2025-07-02T04:18:46.474Z"}
[2025-07-02T04:18:46.577Z] [INFO] GET /check-student-auth - 200 - 66ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"66ms","timestamp":"2025-07-02T04:18:46.577Z"}
[2025-07-02T04:18:47.689Z] [INFO] GET /admin/login - 200 - 94ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"94ms","timestamp":"2025-07-02T04:18:47.689Z"}
[2025-07-02T04:18:50.021Z] [INFO] POST /admin/login - 200 - 136ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"136ms","timestamp":"2025-07-02T04:18:50.021Z"}
[2025-07-02T04:18:50.631Z] [INFO] GET /admin - 200 - 75ms | {"method":"GET","url":"/admin","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"75ms","timestamp":"2025-07-02T04:18:50.631Z"}
[2025-07-02T04:18:50.633Z] [INFO] GET /js/admin-list.js - 200 - 4ms | {"method":"GET","url":"/js/admin-list.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-02T04:18:50.633Z"}
[2025-07-02T04:18:50.636Z] [INFO] GET /js/drag-utils.js - 200 - 2ms | {"method":"GET","url":"/js/drag-utils.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-02T04:18:50.636Z"}
[2025-07-02T04:18:51.426Z] [INFO] GET /?page=1&limit=15&sort=az - 200 - 758ms | {"method":"GET","url":"/?page=1&limit=15&sort=az","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"758ms","timestamp":"2025-07-02T04:18:51.426Z"}
[2025-07-02T04:18:58.135Z] [INFO] GET /admin/new - 200 - 72ms | {"method":"GET","url":"/admin/new","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"72ms","timestamp":"2025-07-02T04:18:58.135Z"}
[2025-07-02T04:18:58.169Z] [INFO] GET /js/admin-stage1-editor.js - 200 - 14ms | {"method":"GET","url":"/js/admin-stage1-editor.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"14ms","timestamp":"2025-07-02T04:18:58.169Z"}
[2025-07-02T04:18:58.173Z] [INFO] GET /js/document-upload.js - 200 - 15ms | {"method":"GET","url":"/js/document-upload.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"15ms","timestamp":"2025-07-02T04:18:58.173Z"}
[2025-07-02T04:18:58.177Z] [INFO] GET /js/storage-recovery.js - 200 - 9ms | {"method":"GET","url":"/js/storage-recovery.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"9ms","timestamp":"2025-07-02T04:18:58.177Z"}
[2025-07-02T04:19:03.843Z] [INFO] GET /history - 200 - 71ms | {"method":"GET","url":"/history","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"71ms","timestamp":"2025-07-02T04:19:03.843Z"}
[2025-07-02T04:19:03.854Z] [INFO] GET /js/history.js - 200 - 19ms | {"method":"GET","url":"/js/history.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"19ms","timestamp":"2025-07-02T04:19:03.854Z"}
[2025-07-02T04:19:04.549Z] [INFO] GET /?page=1&limit=15&sort=time-desc - 200 - 306ms | {"method":"GET","url":"/?page=1&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"306ms","timestamp":"2025-07-02T04:19:04.549Z"}
[2025-07-02T04:19:08.156Z] [INFO] GET /?page=2&limit=15&sort=time-desc - 200 - 143ms | {"method":"GET","url":"/?page=2&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"143ms","timestamp":"2025-07-02T04:19:08.156Z"}
[2025-07-02T04:19:09.739Z] [INFO] GET /?page=3&limit=15&sort=time-desc - 200 - 143ms | {"method":"GET","url":"/?page=3&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"143ms","timestamp":"2025-07-02T04:19:09.739Z"}
[2025-07-02T04:19:11.015Z] [INFO] GET /?page=4&limit=15&sort=time-desc - 200 - 136ms | {"method":"GET","url":"/?page=4&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"136ms","timestamp":"2025-07-02T04:19:11.014Z"}
[2025-07-02T04:19:12.438Z] [INFO] GET /?page=5&limit=15&sort=time-desc - 200 - 139ms | {"method":"GET","url":"/?page=5&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"139ms","timestamp":"2025-07-02T04:19:12.438Z"}
[2025-07-02T04:19:13.369Z] [INFO] GET /?page=7&limit=15&sort=time-desc - 200 - 144ms | {"method":"GET","url":"/?page=7&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"144ms","timestamp":"2025-07-02T04:19:13.369Z"}
[2025-07-02T04:19:14.158Z] [INFO] GET /?page=8&limit=15&sort=time-desc - 200 - 131ms | {"method":"GET","url":"/?page=8&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"131ms","timestamp":"2025-07-02T04:19:14.158Z"}
[2025-07-02T04:19:19.686Z] [INFO] GET /history - 304 - 70ms | {"method":"GET","url":"/history","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"70ms","timestamp":"2025-07-02T04:19:19.686Z"}
[2025-07-02T04:19:20.044Z] [INFO] GET /?page=1&limit=15&sort=time-desc - 200 - 219ms | {"method":"GET","url":"/?page=1&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"219ms","timestamp":"2025-07-02T04:19:20.044Z"}
[2025-07-02T04:19:21.410Z] [INFO] GET /admin/students - 200 - 72ms | {"method":"GET","url":"/admin/students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"72ms","timestamp":"2025-07-02T04:19:21.410Z"}
[2025-07-02T04:19:21.547Z] [INFO] GET /unapproved-students - 200 - 135ms | {"method":"GET","url":"/unapproved-students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"135ms","timestamp":"2025-07-02T04:19:21.547Z"}
[2025-07-02T04:19:22.037Z] [INFO] GET /approved-students - 200 - 624ms | {"method":"GET","url":"/approved-students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"624ms","timestamp":"2025-07-02T04:19:22.037Z"}
[2025-07-02T04:19:23.935Z] [INFO] GET /admin/students - 200 - 76ms | {"method":"GET","url":"/admin/students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"76ms","timestamp":"2025-07-02T04:19:23.935Z"}
[2025-07-02T04:19:24.115Z] [INFO] GET /approved-students - 200 - 156ms | {"method":"GET","url":"/approved-students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"156ms","timestamp":"2025-07-02T04:19:24.115Z"}
[2025-07-02T04:19:24.165Z] [INFO] GET /unapproved-students - 200 - 206ms | {"method":"GET","url":"/unapproved-students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"206ms","timestamp":"2025-07-02T04:19:24.165Z"}
[2025-07-02T04:19:25.765Z] [WARN] 404 Not Found | {"url":"/.well-known/appspecific/com.chrome.devtools.json","method":"GET","ip":"::1"}
[2025-07-02T04:19:25.816Z] [INFO] GET / - 404 - 88ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"88ms","timestamp":"2025-07-02T04:19:25.816Z"}
[2025-07-02T04:19:30.526Z] [INFO] GET /admin/students - 200 - 76ms | {"method":"GET","url":"/admin/students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"76ms","timestamp":"2025-07-02T04:19:30.526Z"}
[2025-07-02T04:19:30.801Z] [INFO] GET /unapproved-students - 200 - 244ms | {"method":"GET","url":"/unapproved-students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"244ms","timestamp":"2025-07-02T04:19:30.801Z"}
[2025-07-02T04:19:30.805Z] [INFO] GET /approved-students - 200 - 247ms | {"method":"GET","url":"/approved-students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"247ms","timestamp":"2025-07-02T04:19:30.805Z"}
[2025-07-02T04:19:48.886Z] [INFO] GET /share/lesson/1744597118421 - 200 - 592ms | {"method":"GET","url":"/share/lesson/1744597118421","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"592ms","timestamp":"2025-07-02T04:19:48.886Z"}
[2025-07-02T04:19:50.500Z] [INFO] GET /lesson/1744597118421 - 200 - 69ms | {"method":"GET","url":"/lesson/1744597118421","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-02T04:19:50.500Z"}
[2025-07-02T04:19:50.601Z] [INFO] GET /check-student-auth - 200 - 70ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"70ms","timestamp":"2025-07-02T04:19:50.601Z"}
[2025-07-02T04:19:50.867Z] [INFO] GET /1744597118421 - 200 - 263ms | {"method":"GET","url":"/1744597118421","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"263ms","timestamp":"2025-07-02T04:19:50.867Z"}
[2025-07-02T04:19:57.067Z] [INFO] GET /check-student-auth - 200 - 75ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"75ms","timestamp":"2025-07-02T04:19:57.067Z"}
[2025-07-02T04:20:02.594Z] [WARN] 404 Not Found | {"url":"/admin/edit/1744603481586","method":"GET","ip":"::1"}
[2025-07-02T04:20:02.630Z] [INFO] GET / - 404 - 70ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"70ms","timestamp":"2025-07-02T04:20:02.630Z"}
[2025-07-02T04:20:34.320Z] [INFO] GET /admin/new - 200 - 319ms | {"method":"GET","url":"/admin/new","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"319ms","timestamp":"2025-07-02T04:20:34.320Z"}
[2025-07-02T04:20:38.316Z] [INFO] GET /admin/configure - 200 - 88ms | {"method":"GET","url":"/admin/configure","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"88ms","timestamp":"2025-07-02T04:20:38.316Z"}
[2025-07-02T04:20:38.318Z] [INFO] GET /js/admin-stage2-configure.js - 200 - 20ms | {"method":"GET","url":"/js/admin-stage2-configure.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"20ms","timestamp":"2025-07-02T04:20:38.318Z"}
[2025-07-02T04:20:47.071Z] [INFO] GET / - 304 - 77ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"77ms","timestamp":"2025-07-02T04:20:47.071Z"}
[2025-07-02T04:20:49.180Z] [INFO] GET /gallery - 304 - 77ms | {"method":"GET","url":"/gallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"77ms","timestamp":"2025-07-02T04:20:49.180Z"}
[2025-07-02T04:20:49.310Z] [INFO] GET /check-student-auth - 200 - 86ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"86ms","timestamp":"2025-07-02T04:20:49.310Z"}
[2025-07-02T04:20:50.620Z] [INFO] GET / - 304 - 78ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"78ms","timestamp":"2025-07-02T04:20:50.620Z"}
[2025-07-02T04:20:51.258Z] [INFO] GET /lessons - 304 - 75ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"75ms","timestamp":"2025-07-02T04:20:51.258Z"}
[2025-07-02T04:20:51.391Z] [INFO] GET /check-student-auth - 200 - 77ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"77ms","timestamp":"2025-07-02T04:20:51.391Z"}
[2025-07-02T04:21:39.153Z] [INFO] GET / - 304 - 382ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"382ms","timestamp":"2025-07-02T04:21:39.153Z"}
[2025-07-02T04:21:42.559Z] [INFO] GET /lessons - 304 - 92ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"92ms","timestamp":"2025-07-02T04:21:42.559Z"}
[2025-07-02T04:21:42.698Z] [INFO] GET /check-student-auth - 200 - 97ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"97ms","timestamp":"2025-07-02T04:21:42.698Z"}
[2025-07-02T04:21:44.423Z] [INFO] GET /quizgame - 200 - 92ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"92ms","timestamp":"2025-07-02T04:21:44.423Z"}
[2025-07-02T04:21:50.331Z] [WARN] 404 Not Found | {"url":"/.well-known/appspecific/com.chrome.devtools.json","method":"GET","ip":"::1"}
[2025-07-02T04:21:50.380Z] [INFO] GET / - 404 - 94ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"94ms","timestamp":"2025-07-02T04:21:50.380Z"}
[2025-07-02T04:21:53.979Z] [INFO] GET / - 304 - 91ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"91ms","timestamp":"2025-07-02T04:21:53.979Z"}
[2025-07-02T04:21:58.323Z] [INFO] GET /gallery - 304 - 92ms | {"method":"GET","url":"/gallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"92ms","timestamp":"2025-07-02T04:21:58.323Z"}
[2025-07-02T04:21:58.485Z] [INFO] GET /check-student-auth - 200 - 100ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"100ms","timestamp":"2025-07-02T04:21:58.485Z"}
[2025-07-02T04:22:00.322Z] [INFO] GET / - 304 - 95ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"95ms","timestamp":"2025-07-02T04:22:00.322Z"}
[2025-07-02T04:22:02.485Z] [INFO] GET /lessons - 304 - 92ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"92ms","timestamp":"2025-07-02T04:22:02.485Z"}
[2025-07-02T04:22:02.632Z] [INFO] GET /check-student-auth - 200 - 97ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"97ms","timestamp":"2025-07-02T04:22:02.632Z"}
