[2025-07-02T03:39:52.185Z] [ACCESS] GET / - 200 - 18ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"18ms","timestamp":"2025-07-02T03:39:52.185Z"}
[2025-07-02T03:39:52.986Z] [ACCESS] GET / - 404 - 11ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"11ms","timestamp":"2025-07-02T03:39:52.983Z"}
[2025-07-02T03:40:07.509Z] [ACCESS] GET /lessons - 200 - 3ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-02T03:40:07.508Z"}
[2025-07-02T03:40:07.816Z] [ACCESS] GET /check-student-auth - 200 - 4ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-02T03:40:07.815Z"}
[2025-07-02T03:40:07.956Z] [ACCESS] GET /student/login?redirect=%2Flessons - 200 - 2ms | {"method":"GET","url":"/student/login?redirect=%2Flessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-02T03:40:07.955Z"}
[2025-07-02T03:40:17.549Z] [ACCESS] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-02T03:40:17.548Z"}
[2025-07-02T03:40:21.678Z] [ACCESS] GET / - 200 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-02T03:40:21.677Z"}
[2025-07-02T03:40:21.721Z] [ACCESS] GET /images/lesson1.jpg - 200 - 6ms | {"method":"GET","url":"/images/lesson1.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-02T03:40:21.720Z"}
[2025-07-02T03:40:21.724Z] [ACCESS] GET /css/style.css - 200 - 10ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"10ms","timestamp":"2025-07-02T03:40:21.723Z"}
[2025-07-02T03:40:21.726Z] [ACCESS] GET /images/lesson2.jpg - 200 - 9ms | {"method":"GET","url":"/images/lesson2.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"9ms","timestamp":"2025-07-02T03:40:21.725Z"}
[2025-07-02T03:40:21.783Z] [ACCESS] GET /images/lesson3.jpg - 200 - 4ms | {"method":"GET","url":"/images/lesson3.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-02T03:40:21.782Z"}
[2025-07-02T03:40:21.786Z] [ACCESS] GET /images/lesson4.jpg - 200 - 5ms | {"method":"GET","url":"/images/lesson4.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-02T03:40:21.785Z"}
[2025-07-02T03:40:21.788Z] [ACCESS] GET /js/network-animation.js - 200 - 7ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"7ms","timestamp":"2025-07-02T03:40:21.787Z"}
[2025-07-02T03:40:21.790Z] [ACCESS] GET /js/landing.js - 200 - 9ms | {"method":"GET","url":"/js/landing.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"9ms","timestamp":"2025-07-02T03:40:21.789Z"}
[2025-07-02T03:40:28.018Z] [ACCESS] GET /lessons - 200 - 2ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-02T03:40:28.016Z"}
[2025-07-02T03:40:28.045Z] [ACCESS] GET /js/index.js - 200 - 2ms | {"method":"GET","url":"/js/index.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-02T03:40:28.044Z"}
[2025-07-02T03:40:28.091Z] [ACCESS] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-02T03:40:28.090Z"}
[2025-07-02T03:40:28.582Z] [ACCESS] GET / - 200 - 479ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"479ms","timestamp":"2025-07-02T03:40:28.581Z"}
[2025-07-02T03:40:28.657Z] [ACCESS] GET /?page=1&limit=10&sort=newest - 200 - 70ms | {"method":"GET","url":"/?page=1&limit=10&sort=newest","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"70ms","timestamp":"2025-07-02T03:40:28.657Z"}
[2025-07-02T03:40:36.733Z] [ACCESS] GET /lesson/1748074653639 - 200 - 1ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-02T03:40:36.732Z"}
[2025-07-02T03:40:36.757Z] [ACCESS] GET /css/lesson-questions.css - 200 - 1ms | {"method":"GET","url":"/css/lesson-questions.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-02T03:40:36.756Z"}
[2025-07-02T03:40:36.764Z] [ACCESS] GET /js/lesson.js - 200 - 3ms | {"method":"GET","url":"/js/lesson.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-02T03:40:36.761Z"}
[2025-07-02T03:40:36.933Z] [ACCESS] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-02T03:40:36.932Z"}
[2025-07-02T03:40:37.168Z] [ACCESS] GET /1748074653639 - 200 - 228ms | {"method":"GET","url":"/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"228ms","timestamp":"2025-07-02T03:40:37.167Z"}
[2025-07-02T03:40:47.672Z] [ACCESS] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-02T03:40:47.672Z"}
[2025-07-02T03:40:50.003Z] [ACCESS] GET /student/login?redirect=%2Flesson%2F1748074653639 - 200 - 2ms | {"method":"GET","url":"/student/login?redirect=%2Flesson%2F1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-02T03:40:50.002Z"}
[2025-07-02T03:40:50.036Z] [ACCESS] GET /js/device-id.js - 200 - 2ms | {"method":"GET","url":"/js/device-id.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-02T03:40:50.034Z"}
[2025-07-02T03:40:58.616Z] [ACCESS] POST /student/login - 200 - 738ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"738ms","timestamp":"2025-07-02T03:40:58.615Z"}
[2025-07-02T03:40:59.214Z] [ACCESS] GET /lesson/1748074653639 - 304 - 66ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"66ms","timestamp":"2025-07-02T03:40:59.213Z"}
[2025-07-02T03:40:59.325Z] [ACCESS] GET /check-student-auth - 200 - 66ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"66ms","timestamp":"2025-07-02T03:40:59.324Z"}
[2025-07-02T03:41:03.730Z] [ACCESS] GET /check-student-auth - 200 - 63ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"63ms","timestamp":"2025-07-02T03:41:03.729Z"}
[2025-07-02T03:41:05.878Z] [ACCESS] GET /student/login?redirect=%2Flesson%2F1748074653639 - 200 - 66ms | {"method":"GET","url":"/student/login?redirect=%2Flesson%2F1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"66ms","timestamp":"2025-07-02T03:41:05.877Z"}
[2025-07-02T03:59:56.730Z] [ACCESS] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-02T03:59:56.729Z"}
[2025-07-02T03:59:58.524Z] [ACCESS] GET /admin - 401 - 2ms | {"method":"GET","url":"/admin","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"2ms","timestamp":"2025-07-02T03:59:58.524Z"}
[2025-07-02T04:00:02.120Z] [ACCESS] GET / - 404 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-02T04:00:02.120Z"}
[2025-07-02T04:01:13.403Z] [ACCESS] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-02T04:01:13.403Z"}
[2025-07-02T04:16:53.356Z] [ACCESS] GET / - 304 - 276ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"276ms","timestamp":"2025-07-02T04:16:53.355Z"}
[2025-07-02T04:16:54.612Z] [ACCESS] GET /quizgame - 200 - 62ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"62ms","timestamp":"2025-07-02T04:16:54.612Z"}
[2025-07-02T04:16:54.618Z] [ACCESS] GET /js/quizgame.js - 200 - 1ms | {"method":"GET","url":"/js/quizgame.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-02T04:16:54.617Z"}
[2025-07-02T04:16:54.781Z] [ACCESS] GET /audio/5sec_1.mp3 - 206 - 6ms | {"method":"GET","url":"/audio/5sec_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"6ms","timestamp":"2025-07-02T04:16:54.780Z"}
[2025-07-02T04:16:54.783Z] [ACCESS] GET /audio/30sec_1.mp3 - 206 - 14ms | {"method":"GET","url":"/audio/30sec_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"14ms","timestamp":"2025-07-02T04:16:54.782Z"}
[2025-07-02T04:16:54.787Z] [ACCESS] GET /audio/5sec_2.mp3 - 206 - 8ms | {"method":"GET","url":"/audio/5sec_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"8ms","timestamp":"2025-07-02T04:16:54.786Z"}
[2025-07-02T04:16:54.789Z] [ACCESS] GET /audio/30sec_2.mp3 - 206 - 18ms | {"method":"GET","url":"/audio/30sec_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"18ms","timestamp":"2025-07-02T04:16:54.789Z"}
[2025-07-02T04:16:54.793Z] [ACCESS] GET /audio/30sec_3.mp3 - 206 - 19ms | {"method":"GET","url":"/audio/30sec_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"19ms","timestamp":"2025-07-02T04:16:54.791Z"}
[2025-07-02T04:16:54.798Z] [ACCESS] GET /audio/5sec_3.mp3 - 206 - 16ms | {"method":"GET","url":"/audio/5sec_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"16ms","timestamp":"2025-07-02T04:16:54.796Z"}
[2025-07-02T04:16:54.800Z] [ACCESS] GET /audio/correct_1.mp3 - 206 - 14ms | {"method":"GET","url":"/audio/correct_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"14ms","timestamp":"2025-07-02T04:16:54.799Z"}
[2025-07-02T04:16:54.804Z] [ACCESS] GET /audio/correct_2.mp3 - 206 - 18ms | {"method":"GET","url":"/audio/correct_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"18ms","timestamp":"2025-07-02T04:16:54.803Z"}
[2025-07-02T04:16:54.808Z] [ACCESS] GET /audio/correct_3.mp3 - 206 - 12ms | {"method":"GET","url":"/audio/correct_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"12ms","timestamp":"2025-07-02T04:16:54.807Z"}
[2025-07-02T04:16:54.812Z] [ACCESS] GET /audio/correct_5.mp3 - 206 - 9ms | {"method":"GET","url":"/audio/correct_5.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"9ms","timestamp":"2025-07-02T04:16:54.811Z"}
[2025-07-02T04:16:54.816Z] [ACCESS] GET /audio/correct_4.mp3 - 206 - 14ms | {"method":"GET","url":"/audio/correct_4.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"14ms","timestamp":"2025-07-02T04:16:54.815Z"}
[2025-07-02T04:16:54.819Z] [ACCESS] GET /audio/incorrect.mp3 - 206 - 12ms | {"method":"GET","url":"/audio/incorrect.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"12ms","timestamp":"2025-07-02T04:16:54.818Z"}
[2025-07-02T04:16:54.822Z] [ACCESS] GET /audio/points.mp3 - 206 - 14ms | {"method":"GET","url":"/audio/points.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"14ms","timestamp":"2025-07-02T04:16:54.820Z"}
[2025-07-02T04:16:57.334Z] [ACCESS] GET /leaderboard - 200 - 65ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"65ms","timestamp":"2025-07-02T04:16:57.333Z"}
[2025-07-02T04:16:57.909Z] [ACCESS] GET /?page=1&filter=all - 200 - 569ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"569ms","timestamp":"2025-07-02T04:16:57.908Z"}
[2025-07-02T04:16:59.007Z] [ACCESS] GET /?page=1&filter=month - 200 - 139ms | {"method":"GET","url":"/?page=1&filter=month","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"139ms","timestamp":"2025-07-02T04:16:59.006Z"}
[2025-07-02T04:17:03.531Z] [ACCESS] GET / - 404 - 64ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"64ms","timestamp":"2025-07-02T04:17:03.530Z"}
[2025-07-02T04:17:27.471Z] [ACCESS] GET / - 304 - 280ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"280ms","timestamp":"2025-07-02T04:17:27.470Z"}
[2025-07-02T04:17:28.210Z] [ACCESS] GET /lessons - 304 - 70ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"70ms","timestamp":"2025-07-02T04:17:28.209Z"}
[2025-07-02T04:17:28.344Z] [ACCESS] GET /check-student-auth - 200 - 70ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"70ms","timestamp":"2025-07-02T04:17:28.343Z"}
[2025-07-02T04:17:28.632Z] [ACCESS] GET /?page=1&limit=10&sort=newest - 200 - 283ms | {"method":"GET","url":"/?page=1&limit=10&sort=newest","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"283ms","timestamp":"2025-07-02T04:17:28.631Z"}
[2025-07-02T04:17:34.741Z] [ACCESS] GET /lesson/1748074653639 - 304 - 69ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"69ms","timestamp":"2025-07-02T04:17:34.741Z"}
[2025-07-02T04:17:34.863Z] [ACCESS] GET /check-student-auth - 200 - 69ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-02T04:17:34.862Z"}
[2025-07-02T04:17:35.170Z] [ACCESS] GET /1748074653639 - 200 - 305ms | {"method":"GET","url":"/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"305ms","timestamp":"2025-07-02T04:17:35.170Z"}
[2025-07-02T04:17:40.181Z] [ACCESS] GET /check-student-auth - 200 - 65ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"65ms","timestamp":"2025-07-02T04:17:40.180Z"}
[2025-07-02T04:17:42.871Z] [ACCESS] GET / - 404 - 68ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"68ms","timestamp":"2025-07-02T04:17:42.870Z"}
[2025-07-02T04:17:49.323Z] [ACCESS] GET / - 304 - 65ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"65ms","timestamp":"2025-07-02T04:17:49.322Z"}
[2025-07-02T04:17:50.141Z] [ACCESS] GET /lessons - 304 - 70ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"70ms","timestamp":"2025-07-02T04:17:50.141Z"}
[2025-07-02T04:17:50.258Z] [ACCESS] GET /check-student-auth - 200 - 68ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-02T04:17:50.257Z"}
[2025-07-02T04:17:52.059Z] [ACCESS] GET /lesson/1747897954640 - 200 - 69ms | {"method":"GET","url":"/lesson/1747897954640","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-02T04:17:52.058Z"}
[2025-07-02T04:17:52.159Z] [ACCESS] GET /check-student-auth - 200 - 68ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-02T04:17:52.158Z"}
[2025-07-02T04:17:52.441Z] [ACCESS] GET /1747897954640 - 200 - 280ms | {"method":"GET","url":"/1747897954640","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"280ms","timestamp":"2025-07-02T04:17:52.441Z"}
[2025-07-02T04:17:57.515Z] [ACCESS] GET /check-student-auth - 200 - 68ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-02T04:17:57.514Z"}
[2025-07-02T04:18:31.373Z] [ACCESS] GET / - 304 - 271ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"271ms","timestamp":"2025-07-02T04:18:31.371Z"}
[2025-07-02T04:18:32.755Z] [ACCESS] GET /lessons - 304 - 67ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"67ms","timestamp":"2025-07-02T04:18:32.755Z"}
[2025-07-02T04:18:32.872Z] [ACCESS] GET /check-student-auth - 200 - 72ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"72ms","timestamp":"2025-07-02T04:18:32.871Z"}
[2025-07-02T04:18:33.611Z] [ACCESS] GET / - 304 - 69ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"69ms","timestamp":"2025-07-02T04:18:33.609Z"}
[2025-07-02T04:18:34.269Z] [ACCESS] GET /js/gallery.js - 200 - 1ms | {"method":"GET","url":"/js/gallery.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-02T04:18:34.267Z"}
[2025-07-02T04:18:34.271Z] [ACCESS] GET /gallery - 200 - 69ms | {"method":"GET","url":"/gallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-02T04:18:34.270Z"}
[2025-07-02T04:18:34.273Z] [ACCESS] GET /css/gallery.css - 200 - 7ms | {"method":"GET","url":"/css/gallery.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"7ms","timestamp":"2025-07-02T04:18:34.272Z"}
[2025-07-02T04:18:34.354Z] [ACCESS] GET /check-student-auth - 200 - 67ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-02T04:18:34.353Z"}
[2025-07-02T04:18:34.434Z] [ACCESS] GET / - 200 - 75ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"75ms","timestamp":"2025-07-02T04:18:34.433Z"}
[2025-07-02T04:18:34.483Z] [ACCESS] GET /lesson_images/1.jpg - 200 - 37ms | {"method":"GET","url":"/lesson_images/1.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"37ms","timestamp":"2025-07-02T04:18:34.482Z"}
[2025-07-02T04:18:34.533Z] [ACCESS] GET /lesson_images/14.jpg - 200 - 70ms | {"method":"GET","url":"/lesson_images/14.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"70ms","timestamp":"2025-07-02T04:18:34.522Z"}
[2025-07-02T04:18:34.567Z] [ACCESS] GET /lesson_images/13.jpg - 200 - 90ms | {"method":"GET","url":"/lesson_images/13.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"90ms","timestamp":"2025-07-02T04:18:34.543Z"}
[2025-07-02T04:18:34.600Z] [ACCESS] GET /lesson_images/11.jpg - 200 - 137ms | {"method":"GET","url":"/lesson_images/11.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"137ms","timestamp":"2025-07-02T04:18:34.587Z"}
[2025-07-02T04:18:34.604Z] [ACCESS] GET /lesson_images/10.jpg - 200 - 156ms | {"method":"GET","url":"/lesson_images/10.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"156ms","timestamp":"2025-07-02T04:18:34.603Z"}
[2025-07-02T04:18:34.630Z] [ACCESS] GET /lesson_images/12.jpg - 200 - 164ms | {"method":"GET","url":"/lesson_images/12.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"164ms","timestamp":"2025-07-02T04:18:34.619Z"}
[2025-07-02T04:18:34.650Z] [ACCESS] GET /lesson_images/18.jpg - 200 - 18ms | {"method":"GET","url":"/lesson_images/18.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"18ms","timestamp":"2025-07-02T04:18:34.649Z"}
[2025-07-02T04:18:34.651Z] [ACCESS] GET /lesson_images/16.jpg - 200 - 75ms | {"method":"GET","url":"/lesson_images/16.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"75ms","timestamp":"2025-07-02T04:18:34.651Z"}
[2025-07-02T04:18:34.653Z] [ACCESS] GET /lesson_images/17.jpg - 200 - 45ms | {"method":"GET","url":"/lesson_images/17.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"45ms","timestamp":"2025-07-02T04:18:34.652Z"}
[2025-07-02T04:18:34.657Z] [ACCESS] GET /lesson_images/2.jpg - 200 - 22ms | {"method":"GET","url":"/lesson_images/2.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"22ms","timestamp":"2025-07-02T04:18:34.655Z"}
[2025-07-02T04:18:34.659Z] [ACCESS] GET /lesson_images/19.jpg - 200 - 26ms | {"method":"GET","url":"/lesson_images/19.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"26ms","timestamp":"2025-07-02T04:18:34.658Z"}
[2025-07-02T04:18:34.662Z] [ACCESS] GET /lesson_images/15.jpg - 200 - 157ms | {"method":"GET","url":"/lesson_images/15.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"157ms","timestamp":"2025-07-02T04:18:34.660Z"}
[2025-07-02T04:18:34.704Z] [ACCESS] GET /lesson_images/22.jpg - 200 - 34ms | {"method":"GET","url":"/lesson_images/22.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"34ms","timestamp":"2025-07-02T04:18:34.702Z"}
[2025-07-02T04:18:34.713Z] [ACCESS] GET /lesson_images/5.jpg - 200 - 42ms | {"method":"GET","url":"/lesson_images/5.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"42ms","timestamp":"2025-07-02T04:18:34.712Z"}
[2025-07-02T04:18:34.717Z] [ACCESS] GET /lesson_images/21.jpg - 200 - 49ms | {"method":"GET","url":"/lesson_images/21.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"49ms","timestamp":"2025-07-02T04:18:34.716Z"}
[2025-07-02T04:18:34.719Z] [ACCESS] GET /lesson_images/3.jpg - 200 - 50ms | {"method":"GET","url":"/lesson_images/3.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"50ms","timestamp":"2025-07-02T04:18:34.718Z"}
[2025-07-02T04:18:34.725Z] [ACCESS] GET /lesson_images/4.jpg - 200 - 55ms | {"method":"GET","url":"/lesson_images/4.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"55ms","timestamp":"2025-07-02T04:18:34.724Z"}
[2025-07-02T04:18:34.732Z] [ACCESS] GET /lesson_images/20.jpg - 200 - 63ms | {"method":"GET","url":"/lesson_images/20.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"63ms","timestamp":"2025-07-02T04:18:34.729Z"}
[2025-07-02T04:18:34.746Z] [ACCESS] GET /lesson_images/6.jpg - 200 - 30ms | {"method":"GET","url":"/lesson_images/6.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"30ms","timestamp":"2025-07-02T04:18:34.745Z"}
[2025-07-02T04:18:34.749Z] [ACCESS] GET /lesson_images/8.jpg - 200 - 15ms | {"method":"GET","url":"/lesson_images/8.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"15ms","timestamp":"2025-07-02T04:18:34.748Z"}
[2025-07-02T04:18:34.755Z] [ACCESS] GET /lesson_images/9.jpg - 200 - 19ms | {"method":"GET","url":"/lesson_images/9.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"19ms","timestamp":"2025-07-02T04:18:34.753Z"}
[2025-07-02T04:18:34.759Z] [ACCESS] GET /lesson_images/7.jpg - 200 - 31ms | {"method":"GET","url":"/lesson_images/7.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"31ms","timestamp":"2025-07-02T04:18:34.757Z"}
[2025-07-02T04:18:45.313Z] [ACCESS] GET / - 304 - 282ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"282ms","timestamp":"2025-07-02T04:18:45.312Z"}
[2025-07-02T04:18:46.475Z] [ACCESS] GET /lessons - 304 - 73ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"73ms","timestamp":"2025-07-02T04:18:46.474Z"}
[2025-07-02T04:18:46.578Z] [ACCESS] GET /check-student-auth - 200 - 66ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"66ms","timestamp":"2025-07-02T04:18:46.577Z"}
[2025-07-02T04:18:47.690Z] [ACCESS] GET /admin/login - 200 - 94ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"94ms","timestamp":"2025-07-02T04:18:47.689Z"}
[2025-07-02T04:18:50.021Z] [ACCESS] POST /admin/login - 200 - 136ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"136ms","timestamp":"2025-07-02T04:18:50.021Z"}
[2025-07-02T04:18:50.632Z] [ACCESS] GET /admin - 200 - 75ms | {"method":"GET","url":"/admin","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"75ms","timestamp":"2025-07-02T04:18:50.631Z"}
[2025-07-02T04:18:50.633Z] [ACCESS] GET /js/admin-list.js - 200 - 4ms | {"method":"GET","url":"/js/admin-list.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-02T04:18:50.633Z"}
[2025-07-02T04:18:50.637Z] [ACCESS] GET /js/drag-utils.js - 200 - 2ms | {"method":"GET","url":"/js/drag-utils.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-02T04:18:50.636Z"}
[2025-07-02T04:18:51.428Z] [ACCESS] GET /?page=1&limit=15&sort=az - 200 - 758ms | {"method":"GET","url":"/?page=1&limit=15&sort=az","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"758ms","timestamp":"2025-07-02T04:18:51.426Z"}
[2025-07-02T04:18:58.135Z] [ACCESS] GET /admin/new - 200 - 72ms | {"method":"GET","url":"/admin/new","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"72ms","timestamp":"2025-07-02T04:18:58.135Z"}
[2025-07-02T04:18:58.171Z] [ACCESS] GET /js/admin-stage1-editor.js - 200 - 14ms | {"method":"GET","url":"/js/admin-stage1-editor.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"14ms","timestamp":"2025-07-02T04:18:58.169Z"}
[2025-07-02T04:18:58.175Z] [ACCESS] GET /js/document-upload.js - 200 - 15ms | {"method":"GET","url":"/js/document-upload.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"15ms","timestamp":"2025-07-02T04:18:58.173Z"}
[2025-07-02T04:18:58.179Z] [ACCESS] GET /js/storage-recovery.js - 200 - 9ms | {"method":"GET","url":"/js/storage-recovery.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"9ms","timestamp":"2025-07-02T04:18:58.177Z"}
[2025-07-02T04:19:03.853Z] [ACCESS] GET /history - 200 - 71ms | {"method":"GET","url":"/history","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"71ms","timestamp":"2025-07-02T04:19:03.843Z"}
[2025-07-02T04:19:03.854Z] [ACCESS] GET /js/history.js - 200 - 19ms | {"method":"GET","url":"/js/history.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"19ms","timestamp":"2025-07-02T04:19:03.854Z"}
[2025-07-02T04:19:04.551Z] [ACCESS] GET /?page=1&limit=15&sort=time-desc - 200 - 306ms | {"method":"GET","url":"/?page=1&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"306ms","timestamp":"2025-07-02T04:19:04.549Z"}
[2025-07-02T04:19:08.157Z] [ACCESS] GET /?page=2&limit=15&sort=time-desc - 200 - 143ms | {"method":"GET","url":"/?page=2&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"143ms","timestamp":"2025-07-02T04:19:08.156Z"}
[2025-07-02T04:19:09.740Z] [ACCESS] GET /?page=3&limit=15&sort=time-desc - 200 - 143ms | {"method":"GET","url":"/?page=3&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"143ms","timestamp":"2025-07-02T04:19:09.739Z"}
[2025-07-02T04:19:11.016Z] [ACCESS] GET /?page=4&limit=15&sort=time-desc - 200 - 136ms | {"method":"GET","url":"/?page=4&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"136ms","timestamp":"2025-07-02T04:19:11.014Z"}
[2025-07-02T04:19:12.439Z] [ACCESS] GET /?page=5&limit=15&sort=time-desc - 200 - 139ms | {"method":"GET","url":"/?page=5&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"139ms","timestamp":"2025-07-02T04:19:12.438Z"}
[2025-07-02T04:19:13.369Z] [ACCESS] GET /?page=7&limit=15&sort=time-desc - 200 - 144ms | {"method":"GET","url":"/?page=7&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"144ms","timestamp":"2025-07-02T04:19:13.369Z"}
[2025-07-02T04:19:14.159Z] [ACCESS] GET /?page=8&limit=15&sort=time-desc - 200 - 131ms | {"method":"GET","url":"/?page=8&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"131ms","timestamp":"2025-07-02T04:19:14.158Z"}
[2025-07-02T04:19:19.687Z] [ACCESS] GET /history - 304 - 70ms | {"method":"GET","url":"/history","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"70ms","timestamp":"2025-07-02T04:19:19.686Z"}
[2025-07-02T04:19:20.045Z] [ACCESS] GET /?page=1&limit=15&sort=time-desc - 200 - 219ms | {"method":"GET","url":"/?page=1&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"219ms","timestamp":"2025-07-02T04:19:20.044Z"}
[2025-07-02T04:19:21.410Z] [ACCESS] GET /admin/students - 200 - 72ms | {"method":"GET","url":"/admin/students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"72ms","timestamp":"2025-07-02T04:19:21.410Z"}
[2025-07-02T04:19:21.548Z] [ACCESS] GET /unapproved-students - 200 - 135ms | {"method":"GET","url":"/unapproved-students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"135ms","timestamp":"2025-07-02T04:19:21.547Z"}
[2025-07-02T04:19:22.038Z] [ACCESS] GET /approved-students - 200 - 624ms | {"method":"GET","url":"/approved-students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"624ms","timestamp":"2025-07-02T04:19:22.037Z"}
[2025-07-02T04:19:23.936Z] [ACCESS] GET /admin/students - 200 - 76ms | {"method":"GET","url":"/admin/students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"76ms","timestamp":"2025-07-02T04:19:23.935Z"}
[2025-07-02T04:19:24.116Z] [ACCESS] GET /approved-students - 200 - 156ms | {"method":"GET","url":"/approved-students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"156ms","timestamp":"2025-07-02T04:19:24.115Z"}
[2025-07-02T04:19:24.166Z] [ACCESS] GET /unapproved-students - 200 - 206ms | {"method":"GET","url":"/unapproved-students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"206ms","timestamp":"2025-07-02T04:19:24.165Z"}
[2025-07-02T04:19:25.817Z] [ACCESS] GET / - 404 - 88ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"88ms","timestamp":"2025-07-02T04:19:25.816Z"}
[2025-07-02T04:19:30.527Z] [ACCESS] GET /admin/students - 200 - 76ms | {"method":"GET","url":"/admin/students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"76ms","timestamp":"2025-07-02T04:19:30.526Z"}
[2025-07-02T04:19:30.802Z] [ACCESS] GET /unapproved-students - 200 - 244ms | {"method":"GET","url":"/unapproved-students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"244ms","timestamp":"2025-07-02T04:19:30.801Z"}
[2025-07-02T04:19:30.806Z] [ACCESS] GET /approved-students - 200 - 247ms | {"method":"GET","url":"/approved-students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"247ms","timestamp":"2025-07-02T04:19:30.805Z"}
[2025-07-02T04:19:48.887Z] [ACCESS] GET /share/lesson/1744597118421 - 200 - 592ms | {"method":"GET","url":"/share/lesson/1744597118421","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"592ms","timestamp":"2025-07-02T04:19:48.886Z"}
[2025-07-02T04:19:50.501Z] [ACCESS] GET /lesson/1744597118421 - 200 - 69ms | {"method":"GET","url":"/lesson/1744597118421","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-02T04:19:50.500Z"}
[2025-07-02T04:19:50.601Z] [ACCESS] GET /check-student-auth - 200 - 70ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"70ms","timestamp":"2025-07-02T04:19:50.601Z"}
[2025-07-02T04:19:50.867Z] [ACCESS] GET /1744597118421 - 200 - 263ms | {"method":"GET","url":"/1744597118421","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"263ms","timestamp":"2025-07-02T04:19:50.867Z"}
[2025-07-02T04:19:57.068Z] [ACCESS] GET /check-student-auth - 200 - 75ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"75ms","timestamp":"2025-07-02T04:19:57.067Z"}
[2025-07-02T04:20:02.631Z] [ACCESS] GET / - 404 - 70ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"70ms","timestamp":"2025-07-02T04:20:02.630Z"}
[2025-07-02T04:20:34.325Z] [ACCESS] GET /admin/new - 200 - 319ms | {"method":"GET","url":"/admin/new","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"319ms","timestamp":"2025-07-02T04:20:34.320Z"}
[2025-07-02T04:20:38.317Z] [ACCESS] GET /admin/configure - 200 - 88ms | {"method":"GET","url":"/admin/configure","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"88ms","timestamp":"2025-07-02T04:20:38.316Z"}
[2025-07-02T04:20:38.318Z] [ACCESS] GET /js/admin-stage2-configure.js - 200 - 20ms | {"method":"GET","url":"/js/admin-stage2-configure.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"20ms","timestamp":"2025-07-02T04:20:38.318Z"}
[2025-07-02T04:20:47.072Z] [ACCESS] GET / - 304 - 77ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"77ms","timestamp":"2025-07-02T04:20:47.071Z"}
[2025-07-02T04:20:49.180Z] [ACCESS] GET /gallery - 304 - 77ms | {"method":"GET","url":"/gallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"77ms","timestamp":"2025-07-02T04:20:49.180Z"}
[2025-07-02T04:20:49.311Z] [ACCESS] GET /check-student-auth - 200 - 86ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"86ms","timestamp":"2025-07-02T04:20:49.310Z"}
[2025-07-02T04:20:50.621Z] [ACCESS] GET / - 304 - 78ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"78ms","timestamp":"2025-07-02T04:20:50.620Z"}
[2025-07-02T04:20:51.259Z] [ACCESS] GET /lessons - 304 - 75ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"75ms","timestamp":"2025-07-02T04:20:51.258Z"}
[2025-07-02T04:20:51.393Z] [ACCESS] GET /check-student-auth - 200 - 77ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"77ms","timestamp":"2025-07-02T04:20:51.391Z"}
[2025-07-02T04:21:39.154Z] [ACCESS] GET / - 304 - 382ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"382ms","timestamp":"2025-07-02T04:21:39.153Z"}
[2025-07-02T04:21:42.560Z] [ACCESS] GET /lessons - 304 - 92ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"92ms","timestamp":"2025-07-02T04:21:42.559Z"}
[2025-07-02T04:21:42.699Z] [ACCESS] GET /check-student-auth - 200 - 97ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"97ms","timestamp":"2025-07-02T04:21:42.698Z"}
[2025-07-02T04:21:44.424Z] [ACCESS] GET /quizgame - 200 - 92ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"92ms","timestamp":"2025-07-02T04:21:44.423Z"}
[2025-07-02T04:21:50.381Z] [ACCESS] GET / - 404 - 94ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"94ms","timestamp":"2025-07-02T04:21:50.380Z"}
[2025-07-02T04:21:53.980Z] [ACCESS] GET / - 304 - 91ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"91ms","timestamp":"2025-07-02T04:21:53.979Z"}
[2025-07-02T04:21:58.323Z] [ACCESS] GET /gallery - 304 - 92ms | {"method":"GET","url":"/gallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"92ms","timestamp":"2025-07-02T04:21:58.323Z"}
[2025-07-02T04:21:58.486Z] [ACCESS] GET /check-student-auth - 200 - 100ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"100ms","timestamp":"2025-07-02T04:21:58.485Z"}
[2025-07-02T04:22:00.323Z] [ACCESS] GET / - 304 - 95ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"95ms","timestamp":"2025-07-02T04:22:00.322Z"}
[2025-07-02T04:22:02.485Z] [ACCESS] GET /lessons - 304 - 92ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"92ms","timestamp":"2025-07-02T04:22:02.485Z"}
[2025-07-02T04:22:02.632Z] [ACCESS] GET /check-student-auth - 200 - 97ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"97ms","timestamp":"2025-07-02T04:22:02.632Z"}
