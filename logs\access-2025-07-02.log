[2025-07-02T05:05:00.721Z] [ACCESS] GET /css/style.css - 200 - 14ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"14ms","timestamp":"2025-07-02T05:05:00.720Z"}
[2025-07-02T05:05:00.885Z] [ACCESS] GET /js/network-animation.js - 200 - 3ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-02T05:05:00.884Z"}
[2025-07-02T05:05:00.887Z] [ACCESS] GET /js/landing.js - 200 - 5ms | {"method":"GET","url":"/js/landing.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-02T05:05:00.887Z"}
[2025-07-02T05:05:04.689Z] [ACCESS] GET /lessons - 302 - 6ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":302,"responseTime":"6ms","timestamp":"2025-07-02T05:05:04.688Z"}
[2025-07-02T05:05:04.695Z] [ACCESS] GET /student/login?redirect=%2Flessons - 200 - 2ms | {"method":"GET","url":"/student/login?redirect=%2Flessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-02T05:05:04.695Z"}
[2025-07-02T05:05:04.741Z] [ACCESS] GET /js/device-id.js - 200 - 4ms | {"method":"GET","url":"/js/device-id.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-02T05:05:04.740Z"}
[2025-07-02T05:05:04.746Z] [ACCESS] GET /css/style.css - 200 - 7ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"7ms","timestamp":"2025-07-02T05:05:04.743Z"}
[2025-07-02T05:05:07.402Z] [ACCESS] POST / - 404 - 17ms | {"method":"POST","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"17ms","timestamp":"2025-07-02T05:05:07.401Z"}
[2025-07-02T05:05:11.824Z] [ACCESS] GET /css/style.css - 200 - 4ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-02T05:05:11.823Z"}
[2025-07-02T05:05:11.865Z] [ACCESS] GET / - 404 - 8ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"8ms","timestamp":"2025-07-02T05:05:11.863Z"}
