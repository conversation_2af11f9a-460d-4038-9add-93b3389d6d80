[2025-07-01T18:41:17.627Z] [ACCESS] GET / - 200 - 13ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"13ms","timestamp":"2025-07-01T18:41:17.622Z"}
[2025-07-01T18:41:17.683Z] [ACCESS] GET /css/style.css - 200 - 6ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-01T18:41:17.682Z"}
[2025-07-01T18:41:17.686Z] [ACCESS] GET /images/lesson1.jpg - 200 - 6ms | {"method":"GET","url":"/images/lesson1.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-01T18:41:17.685Z"}
[2025-07-01T18:41:17.688Z] [ACCESS] GET /images/lesson2.jpg - 200 - 7ms | {"method":"GET","url":"/images/lesson2.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"7ms","timestamp":"2025-07-01T18:41:17.687Z"}
[2025-07-01T18:41:17.761Z] [ACCESS] GET /images/lesson4.jpg - 200 - 6ms | {"method":"GET","url":"/images/lesson4.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-01T18:41:17.760Z"}
[2025-07-01T18:41:17.763Z] [ACCESS] GET /images/lesson3.jpg - 200 - 9ms | {"method":"GET","url":"/images/lesson3.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"9ms","timestamp":"2025-07-01T18:41:17.762Z"}
[2025-07-01T18:41:17.764Z] [ACCESS] GET /js/network-animation.js - 200 - 9ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"9ms","timestamp":"2025-07-01T18:41:17.764Z"}
[2025-07-01T18:41:17.766Z] [ACCESS] GET /js/landing.js - 200 - 10ms | {"method":"GET","url":"/js/landing.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"10ms","timestamp":"2025-07-01T18:41:17.766Z"}
[2025-07-01T18:41:50.244Z] [ACCESS] GET / - 304 - 9ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"9ms","timestamp":"2025-07-01T18:41:50.243Z"}
[2025-07-01T18:41:50.580Z] [ACCESS] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T18:41:50.580Z"}
[2025-07-01T18:41:53.700Z] [ACCESS] GET /lessons - 200 - 5ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-01T18:41:53.699Z"}
[2025-07-01T18:41:53.726Z] [ACCESS] GET /js/index.js - 200 - 2ms | {"method":"GET","url":"/js/index.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T18:41:53.724Z"}
[2025-07-01T18:41:53.738Z] [ACCESS] GET /check-student-auth - 200 - 2ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T18:41:53.737Z"}
[2025-07-01T18:41:54.359Z] [ACCESS] GET / - 200 - 578ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"578ms","timestamp":"2025-07-01T18:41:54.358Z"}
[2025-07-01T18:41:54.464Z] [ACCESS] GET /?page=1&limit=10&sort=newest - 200 - 100ms | {"method":"GET","url":"/?page=1&limit=10&sort=newest","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"100ms","timestamp":"2025-07-01T18:41:54.463Z"}
[2025-07-01T18:41:57.163Z] [ACCESS] GET /lessons - 304 - 1ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T18:41:57.162Z"}
[2025-07-01T18:41:57.202Z] [ACCESS] GET /check-student-auth - 200 - 3ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T18:41:57.201Z"}
[2025-07-01T18:42:00.052Z] [ACCESS] GET /quizgame - 200 - 2ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T18:42:00.051Z"}
[2025-07-01T18:42:00.084Z] [ACCESS] GET /js/quizgame.js - 200 - 4ms | {"method":"GET","url":"/js/quizgame.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T18:42:00.083Z"}
[2025-07-01T18:42:00.251Z] [ACCESS] GET /audio/5sec_1.mp3 - 206 - 15ms | {"method":"GET","url":"/audio/5sec_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"15ms","timestamp":"2025-07-01T18:42:00.250Z"}
[2025-07-01T18:42:00.257Z] [ACCESS] GET /audio/5sec_2.mp3 - 206 - 19ms | {"method":"GET","url":"/audio/5sec_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"19ms","timestamp":"2025-07-01T18:42:00.255Z"}
[2025-07-01T18:42:00.260Z] [ACCESS] GET /audio/5sec_3.mp3 - 206 - 22ms | {"method":"GET","url":"/audio/5sec_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"22ms","timestamp":"2025-07-01T18:42:00.258Z"}
[2025-07-01T18:42:00.265Z] [ACCESS] GET /audio/30sec_1.mp3 - 206 - 43ms | {"method":"GET","url":"/audio/30sec_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"43ms","timestamp":"2025-07-01T18:42:00.263Z"}
[2025-07-01T18:42:00.275Z] [ACCESS] GET /audio/correct_1.mp3 - 206 - 11ms | {"method":"GET","url":"/audio/correct_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"11ms","timestamp":"2025-07-01T18:42:00.273Z"}
[2025-07-01T18:42:00.278Z] [ACCESS] GET /audio/30sec_2.mp3 - 206 - 52ms | {"method":"GET","url":"/audio/30sec_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"52ms","timestamp":"2025-07-01T18:42:00.277Z"}
[2025-07-01T18:42:00.281Z] [ACCESS] GET /audio/30sec_3.mp3 - 206 - 51ms | {"method":"GET","url":"/audio/30sec_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"51ms","timestamp":"2025-07-01T18:42:00.280Z"}
[2025-07-01T18:42:00.283Z] [ACCESS] GET /audio/correct_2.mp3 - 206 - 14ms | {"method":"GET","url":"/audio/correct_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"14ms","timestamp":"2025-07-01T18:42:00.282Z"}
[2025-07-01T18:42:00.285Z] [ACCESS] GET /audio/correct_3.mp3 - 206 - 16ms | {"method":"GET","url":"/audio/correct_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"16ms","timestamp":"2025-07-01T18:42:00.284Z"}
[2025-07-01T18:42:00.291Z] [ACCESS] GET /audio/correct_4.mp3 - 206 - 19ms | {"method":"GET","url":"/audio/correct_4.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"19ms","timestamp":"2025-07-01T18:42:00.289Z"}
[2025-07-01T18:42:00.295Z] [ACCESS] GET /audio/correct_5.mp3 - 206 - 8ms | {"method":"GET","url":"/audio/correct_5.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"8ms","timestamp":"2025-07-01T18:42:00.294Z"}
[2025-07-01T18:42:00.299Z] [ACCESS] GET /audio/incorrect.mp3 - 206 - 11ms | {"method":"GET","url":"/audio/incorrect.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"11ms","timestamp":"2025-07-01T18:42:00.298Z"}
[2025-07-01T18:42:00.302Z] [ACCESS] GET /audio/points.mp3 - 206 - 13ms | {"method":"GET","url":"/audio/points.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"13ms","timestamp":"2025-07-01T18:42:00.300Z"}
[2025-07-01T18:42:03.417Z] [ACCESS] GET / - 304 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"3ms","timestamp":"2025-07-01T18:42:03.416Z"}
[2025-07-01T18:42:04.157Z] [ACCESS] GET /leaderboard - 200 - 1ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T18:42:04.156Z"}
[2025-07-01T18:42:04.197Z] [ACCESS] GET /?page=1&filter=all - 404 - 6ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"6ms","timestamp":"2025-07-01T18:42:04.196Z"}
[2025-07-01T18:42:06.180Z] [ACCESS] GET /leaderboard - 304 - 3ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"3ms","timestamp":"2025-07-01T18:42:06.179Z"}
[2025-07-01T18:42:06.220Z] [ACCESS] GET /?page=1&filter=all - 404 - 3ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T18:42:06.219Z"}
[2025-07-01T18:42:11.004Z] [ACCESS] GET /?page=1&filter=month - 404 - 3ms | {"method":"GET","url":"/?page=1&filter=month","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T18:42:11.003Z"}
[2025-07-01T18:42:11.864Z] [ACCESS] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T18:42:11.863Z"}
[2025-07-01T18:42:12.853Z] [ACCESS] GET /lessons - 304 - 1ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T18:42:12.852Z"}
[2025-07-01T18:42:12.893Z] [ACCESS] GET /check-student-auth - 200 - 2ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T18:42:12.892Z"}
[2025-07-01T18:42:14.233Z] [ACCESS] GET /admin/login - 200 - 2ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T18:42:14.232Z"}
[2025-07-01T18:42:16.974Z] [ACCESS] POST /admin/login - 200 - 403ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"403ms","timestamp":"2025-07-01T18:42:16.973Z"}
[2025-07-01T18:42:17.546Z] [ACCESS] GET /js/drag-utils.js - 200 - 3ms | {"method":"GET","url":"/js/drag-utils.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T18:42:17.546Z"}
[2025-07-01T18:42:17.559Z] [ACCESS] GET /admin - 200 - 77ms | {"method":"GET","url":"/admin","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"77ms","timestamp":"2025-07-01T18:42:17.559Z"}
[2025-07-01T18:42:17.561Z] [ACCESS] GET /js/admin-list.js - 200 - 19ms | {"method":"GET","url":"/js/admin-list.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"19ms","timestamp":"2025-07-01T18:42:17.560Z"}
[2025-07-01T18:42:18.052Z] [ACCESS] GET /?page=1&limit=15&sort=az - 200 - 473ms | {"method":"GET","url":"/?page=1&limit=15&sort=az","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"473ms","timestamp":"2025-07-01T18:42:18.051Z"}
[2025-07-01T18:42:24.867Z] [ACCESS] GET /history - 200 - 67ms | {"method":"GET","url":"/history","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T18:42:24.866Z"}
[2025-07-01T18:42:24.869Z] [ACCESS] GET /js/history.js - 200 - 14ms | {"method":"GET","url":"/js/history.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"14ms","timestamp":"2025-07-01T18:42:24.868Z"}
[2025-07-01T18:42:25.848Z] [ACCESS] GET /?page=1&limit=15&sort=time-desc - 200 - 676ms | {"method":"GET","url":"/?page=1&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"676ms","timestamp":"2025-07-01T18:42:25.847Z"}
[2025-07-01T18:42:45.804Z] [ACCESS] GET /admin - 200 - 257ms | {"method":"GET","url":"/admin","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"257ms","timestamp":"2025-07-01T18:42:45.804Z"}
[2025-07-01T18:42:51.518Z] [ACCESS] GET / - 404 - 64ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"64ms","timestamp":"2025-07-01T18:42:51.518Z"}
[2025-07-01T18:43:04.638Z] [ACCESS] GET /admin/students - 200 - 284ms | {"method":"GET","url":"/admin/students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"284ms","timestamp":"2025-07-01T18:43:04.638Z"}
[2025-07-01T18:43:04.709Z] [ACCESS] GET / - 404 - 67ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"67ms","timestamp":"2025-07-01T18:43:04.709Z"}
[2025-07-01T18:43:04.938Z] [ACCESS] GET / - 404 - 295ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"295ms","timestamp":"2025-07-01T18:43:04.938Z"}
[2025-07-01T18:43:09.365Z] [ACCESS] GET /admin/students - 200 - 71ms | {"method":"GET","url":"/admin/students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"71ms","timestamp":"2025-07-01T18:43:09.365Z"}
[2025-07-01T18:43:09.425Z] [ACCESS] GET / - 404 - 63ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"63ms","timestamp":"2025-07-01T18:43:09.424Z"}
[2025-07-01T18:43:09.666Z] [ACCESS] GET / - 404 - 304ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"304ms","timestamp":"2025-07-01T18:43:09.665Z"}
[2025-07-01T18:43:13.441Z] [ACCESS] GET / - 304 - 71ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"71ms","timestamp":"2025-07-01T18:43:13.440Z"}
[2025-07-01T18:43:14.743Z] [ACCESS] GET /lessons - 304 - 74ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"74ms","timestamp":"2025-07-01T18:43:14.742Z"}
[2025-07-01T18:43:14.855Z] [ACCESS] GET /check-student-auth - 200 - 72ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"72ms","timestamp":"2025-07-01T18:43:14.853Z"}
[2025-07-01T18:43:16.535Z] [ACCESS] GET /js/lesson.js - 200 - 1ms | {"method":"GET","url":"/js/lesson.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T18:43:16.534Z"}
[2025-07-01T18:43:16.539Z] [ACCESS] GET /lesson/1748074653639 - 200 - 71ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"71ms","timestamp":"2025-07-01T18:43:16.538Z"}
[2025-07-01T18:43:16.590Z] [ACCESS] GET / - 404 - 66ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"66ms","timestamp":"2025-07-01T18:43:16.590Z"}
[2025-07-01T18:43:16.649Z] [ACCESS] GET /check-student-auth - 200 - 71ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"71ms","timestamp":"2025-07-01T18:43:16.647Z"}
[2025-07-01T18:43:17.016Z] [ACCESS] GET /1748074653639 - 200 - 362ms | {"method":"GET","url":"/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"362ms","timestamp":"2025-07-01T18:43:17.014Z"}
[2025-07-01T18:43:21.612Z] [ACCESS] GET /lesson/1748074653639 - 304 - 72ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"72ms","timestamp":"2025-07-01T18:43:21.611Z"}
[2025-07-01T18:43:21.704Z] [ACCESS] GET / - 404 - 73ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"73ms","timestamp":"2025-07-01T18:43:21.703Z"}
[2025-07-01T18:43:21.742Z] [ACCESS] GET /check-student-auth - 200 - 62ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"62ms","timestamp":"2025-07-01T18:43:21.741Z"}
[2025-07-01T18:43:26.673Z] [ACCESS] GET /?page=1&limit=10&sort=oldest - 200 - 307ms | {"method":"GET","url":"/?page=1&limit=10&sort=oldest","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"307ms","timestamp":"2025-07-01T18:43:26.672Z"}
[2025-07-01T18:43:29.373Z] [ACCESS] GET /lesson/1738739931367 - 200 - 62ms | {"method":"GET","url":"/lesson/1738739931367","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"62ms","timestamp":"2025-07-01T18:43:29.372Z"}
[2025-07-01T18:43:29.438Z] [ACCESS] GET / - 404 - 74ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"74ms","timestamp":"2025-07-01T18:43:29.437Z"}
[2025-07-01T18:43:29.476Z] [ACCESS] GET /check-student-auth - 200 - 61ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"61ms","timestamp":"2025-07-01T18:43:29.475Z"}
[2025-07-01T18:43:29.725Z] [ACCESS] GET /1738739931367 - 200 - 247ms | {"method":"GET","url":"/1738739931367","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"247ms","timestamp":"2025-07-01T18:43:29.724Z"}
[2025-07-01T18:44:22.391Z] [ACCESS] GET / - 304 - 317ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"317ms","timestamp":"2025-07-01T18:44:22.390Z"}
[2025-07-01T18:44:26.294Z] [ACCESS] GET /lessons - 304 - 67ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"67ms","timestamp":"2025-07-01T18:44:26.293Z"}
[2025-07-01T18:44:26.414Z] [ACCESS] GET /check-student-auth - 200 - 69ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-01T18:44:26.413Z"}
[2025-07-01T18:44:35.334Z] [ACCESS] GET / - 304 - 69ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"69ms","timestamp":"2025-07-01T18:44:35.332Z"}
[2025-07-01T18:45:10.842Z] [ACCESS] GET /quizgame - 200 - 264ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"264ms","timestamp":"2025-07-01T18:45:10.840Z"}
[2025-07-01T18:45:13.142Z] [ACCESS] GET /leaderboard - 304 - 60ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"60ms","timestamp":"2025-07-01T18:45:13.141Z"}
[2025-07-01T18:45:13.250Z] [ACCESS] GET /?page=1&filter=all - 404 - 63ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"63ms","timestamp":"2025-07-01T18:45:13.249Z"}
[2025-07-01T18:45:15.752Z] [ACCESS] GET / - 404 - 62ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"62ms","timestamp":"2025-07-01T18:45:15.750Z"}
[2025-07-01T18:45:17.986Z] [ACCESS] GET /leaderboard - 304 - 60ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":304,"responseTime":"60ms","timestamp":"2025-07-01T18:45:17.986Z"}
[2025-07-01T18:45:18.088Z] [ACCESS] GET / - 404 - 66ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"66ms","timestamp":"2025-07-01T18:45:18.082Z"}
[2025-07-01T18:45:18.216Z] [ACCESS] GET /?page=1&filter=all - 404 - 68ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":404,"responseTime":"68ms","timestamp":"2025-07-01T18:45:18.216Z"}
[2025-07-01T18:45:43.603Z] [ACCESS] GET / - 304 - 258ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"258ms","timestamp":"2025-07-01T18:45:43.602Z"}
[2025-07-01T18:45:45.208Z] [ACCESS] GET /lessons - 304 - 53ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"53ms","timestamp":"2025-07-01T18:45:45.207Z"}
[2025-07-01T18:45:45.312Z] [ACCESS] GET /check-student-auth - 200 - 55ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"55ms","timestamp":"2025-07-01T18:45:45.312Z"}
[2025-07-01T18:45:48.452Z] [ACCESS] GET /lesson/1748074653639 - 304 - 55ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"55ms","timestamp":"2025-07-01T18:45:48.451Z"}
[2025-07-01T18:45:48.533Z] [ACCESS] GET / - 404 - 56ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"56ms","timestamp":"2025-07-01T18:45:48.532Z"}
[2025-07-01T18:45:48.748Z] [ACCESS] GET /check-student-auth - 200 - 230ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"230ms","timestamp":"2025-07-01T18:45:48.747Z"}
[2025-07-01T18:45:50.181Z] [ACCESS] GET / - 404 - 55ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"55ms","timestamp":"2025-07-01T18:45:50.180Z"}
[2025-07-01T18:45:50.197Z] [ACCESS] GET / - 404 - 57ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"57ms","timestamp":"2025-07-01T18:45:50.196Z"}
[2025-07-01T18:46:21.648Z] [ACCESS] GET / - 404 - 263ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"263ms","timestamp":"2025-07-01T18:46:21.648Z"}
[2025-07-01T18:47:24.462Z] [ACCESS] GET /check-student-auth - 200 - 229ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"229ms","timestamp":"2025-07-01T18:47:24.462Z"}
[2025-07-01T18:47:26.085Z] [ACCESS] GET /admin/login - 200 - 69ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-01T18:47:26.084Z"}
[2025-07-01T18:47:28.317Z] [ACCESS] POST /admin/login - 400 - 63ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"63ms","timestamp":"2025-07-01T18:47:28.316Z"}
[2025-07-01T18:47:31.742Z] [ACCESS] GET / - 404 - 59ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"59ms","timestamp":"2025-07-01T18:47:31.741Z"}
[2025-07-01T18:47:35.891Z] [ACCESS] POST /admin/login - 400 - 57ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":400,"responseTime":"57ms","timestamp":"2025-07-01T18:47:35.890Z"}
[2025-07-01T18:47:53.156Z] [ACCESS] GET / - 304 - 271ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"271ms","timestamp":"2025-07-01T18:47:53.155Z"}
[2025-07-01T18:47:55.470Z] [ACCESS] GET /lessons - 304 - 73ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"73ms","timestamp":"2025-07-01T18:47:55.468Z"}
[2025-07-01T18:47:55.578Z] [ACCESS] GET /check-student-auth - 200 - 68ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-01T18:47:55.578Z"}
[2025-07-01T18:48:03.522Z] [ACCESS] GET /?page=1&limit=10&sort=newest&search=dda - 200 - 677ms | {"method":"GET","url":"/?page=1&limit=10&sort=newest&search=dda","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"677ms","timestamp":"2025-07-01T18:48:03.521Z"}
[2025-07-01T18:48:08.895Z] [ACCESS] GET /?page=1&limit=10&sort=newest&search=vl11 - 200 - 317ms | {"method":"GET","url":"/?page=1&limit=10&sort=newest&search=vl11","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"317ms","timestamp":"2025-07-01T18:48:08.895Z"}
[2025-07-01T18:48:12.759Z] [ACCESS] GET / - 304 - 68ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"68ms","timestamp":"2025-07-01T18:48:12.758Z"}
[2025-07-01T18:48:14.929Z] [ACCESS] GET /quizgame - 200 - 66ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"66ms","timestamp":"2025-07-01T18:48:14.928Z"}
[2025-07-01T18:48:18.832Z] [ACCESS] GET / - 404 - 68ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"68ms","timestamp":"2025-07-01T18:48:18.831Z"}
[2025-07-01T18:48:23.300Z] [ACCESS] GET /quizgame - 200 - 68ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-01T18:48:23.299Z"}
[2025-07-01T18:48:23.381Z] [ACCESS] GET /quizgame - 200 - 67ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T18:48:23.380Z"}
[2025-07-01T18:48:23.555Z] [ACCESS] GET / - 404 - 271ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"271ms","timestamp":"2025-07-01T18:48:23.554Z"}
[2025-07-01T18:48:23.599Z] [ACCESS] GET / - 404 - 74ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"74ms","timestamp":"2025-07-01T18:48:23.597Z"}
[2025-07-01T18:49:28.205Z] [ACCESS] GET / - 200 - 278ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"278ms","timestamp":"2025-07-01T18:49:28.204Z"}
[2025-07-01T18:49:29.040Z] [ACCESS] GET /js/gallery.js - 200 - 1ms | {"method":"GET","url":"/js/gallery.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T18:49:29.039Z"}
[2025-07-01T18:49:29.052Z] [ACCESS] GET /gallery - 200 - 70ms | {"method":"GET","url":"/gallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"70ms","timestamp":"2025-07-01T18:49:29.051Z"}
[2025-07-01T18:49:29.335Z] [ACCESS] GET / - 404 - 299ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"299ms","timestamp":"2025-07-01T18:49:29.335Z"}
[2025-07-01T18:49:29.376Z] [ACCESS] GET /check-student-auth - 200 - 67ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T18:49:29.375Z"}
[2025-07-01T18:49:29.451Z] [ACCESS] GET / - 404 - 72ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"72ms","timestamp":"2025-07-01T18:49:29.450Z"}
[2025-07-01T18:49:32.131Z] [ACCESS] GET / - 404 - 70ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"70ms","timestamp":"2025-07-01T18:49:32.130Z"}
[2025-07-01T18:49:32.161Z] [ACCESS] GET / - 404 - 79ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"79ms","timestamp":"2025-07-01T18:49:32.160Z"}
[2025-07-01T18:49:58.260Z] [ACCESS] GET / - 404 - 230ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"230ms","timestamp":"2025-07-01T18:49:58.259Z"}
[2025-07-01T18:51:53.874Z] [ACCESS] GET /gallery - 304 - 534ms | {"method":"GET","url":"/gallery","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":304,"responseTime":"534ms","timestamp":"2025-07-01T18:51:53.873Z"}
[2025-07-01T18:51:53.931Z] [ACCESS] GET /css/gallery.css - 200 - 3ms | {"method":"GET","url":"/css/gallery.css","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T18:51:53.930Z"}
[2025-07-01T18:51:54.094Z] [ACCESS] GET / - 404 - 162ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"162ms","timestamp":"2025-07-01T18:51:54.094Z"}
[2025-07-01T18:51:54.492Z] [ACCESS] GET /check-student-auth - 200 - 467ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":200,"responseTime":"467ms","timestamp":"2025-07-01T18:51:54.491Z"}
[2025-07-01T18:51:54.672Z] [ACCESS] GET / - 404 - 175ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":404,"responseTime":"175ms","timestamp":"2025-07-01T18:51:54.672Z"}
[2025-07-01T18:51:57.411Z] [ACCESS] GET / - 304 - 62ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"62ms","timestamp":"2025-07-01T18:51:57.410Z"}
[2025-07-01T18:51:58.579Z] [ACCESS] GET /lessons - 304 - 58ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"58ms","timestamp":"2025-07-01T18:51:58.578Z"}
[2025-07-01T18:51:58.685Z] [ACCESS] GET /check-student-auth - 200 - 59ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"59ms","timestamp":"2025-07-01T18:51:58.684Z"}
[2025-07-01T18:51:59.239Z] [ACCESS] GET /?page=1&limit=10&sort=newest - 200 - 548ms | {"method":"GET","url":"/?page=1&limit=10&sort=newest","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"548ms","timestamp":"2025-07-01T18:51:59.238Z"}
[2025-07-01T18:52:00.382Z] [ACCESS] GET /lesson/1748074653639 - 304 - 59ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"59ms","timestamp":"2025-07-01T18:52:00.381Z"}
[2025-07-01T18:52:00.412Z] [ACCESS] GET /css/lesson-questions.css - 200 - 2ms | {"method":"GET","url":"/css/lesson-questions.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T18:52:00.412Z"}
[2025-07-01T18:52:00.519Z] [ACCESS] GET /check-student-auth - 200 - 61ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"61ms","timestamp":"2025-07-01T18:52:00.518Z"}
[2025-07-01T18:52:02.068Z] [ACCESS] GET /lesson/1748074653639 - 304 - 58ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"58ms","timestamp":"2025-07-01T18:52:02.067Z"}
[2025-07-01T18:52:02.167Z] [ACCESS] GET /check-student-auth - 200 - 59ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"59ms","timestamp":"2025-07-01T18:52:02.166Z"}
[2025-07-01T18:52:03.862Z] [ACCESS] GET / - 404 - 60ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"60ms","timestamp":"2025-07-01T18:52:03.861Z"}
[2025-07-01T18:53:36.460Z] [ACCESS] GET /lesson/1748074653639 - 304 - 300ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"300ms","timestamp":"2025-07-01T18:53:36.459Z"}
[2025-07-01T18:53:36.648Z] [ACCESS] GET /check-student-auth - 200 - 75ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"75ms","timestamp":"2025-07-01T18:53:36.647Z"}
[2025-07-01T18:53:37.029Z] [ACCESS] GET /1748074653639 - 200 - 378ms | {"method":"GET","url":"/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"378ms","timestamp":"2025-07-01T18:53:37.028Z"}
[2025-07-01T18:53:51.706Z] [ACCESS] GET /lesson/1748074653639 - 304 - 280ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"280ms","timestamp":"2025-07-01T18:53:51.704Z"}
[2025-07-01T18:53:51.859Z] [ACCESS] GET /check-student-auth - 200 - 67ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T18:53:51.858Z"}
[2025-07-01T18:55:17.205Z] [ACCESS] GET /lesson/1748074653639 - 304 - 294ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"294ms","timestamp":"2025-07-01T18:55:17.204Z"}
[2025-07-01T18:55:17.354Z] [ACCESS] GET /check-student-auth - 200 - 75ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"75ms","timestamp":"2025-07-01T18:55:17.353Z"}
[2025-07-01T18:55:19.569Z] [ACCESS] GET /check-student-auth - 200 - 68ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-01T18:55:19.568Z"}
[2025-07-01T18:55:20.928Z] [ACCESS] GET / - 304 - 69ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"69ms","timestamp":"2025-07-01T18:55:20.928Z"}
[2025-07-01T18:55:21.789Z] [ACCESS] GET /quizgame - 200 - 71ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"71ms","timestamp":"2025-07-01T18:55:21.789Z"}
[2025-07-01T18:55:23.418Z] [ACCESS] GET /leaderboard - 304 - 71ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"71ms","timestamp":"2025-07-01T18:55:23.417Z"}
[2025-07-01T18:55:23.542Z] [ACCESS] GET /?page=1&filter=all - 404 - 75ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"75ms","timestamp":"2025-07-01T18:55:23.542Z"}
[2025-07-01T18:55:25.367Z] [ACCESS] GET /leaderboard - 304 - 69ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"69ms","timestamp":"2025-07-01T18:55:25.366Z"}
[2025-07-01T18:55:25.467Z] [ACCESS] GET /?page=1&filter=all - 404 - 69ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"69ms","timestamp":"2025-07-01T18:55:25.467Z"}
[2025-07-01T18:55:29.162Z] [ACCESS] GET /leaderboard - 304 - 373ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"373ms","timestamp":"2025-07-01T18:55:29.161Z"}
[2025-07-01T18:55:29.802Z] [ACCESS] GET /?page=1&filter=all - 200 - 600ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"600ms","timestamp":"2025-07-01T18:55:29.802Z"}
[2025-07-01T18:55:30.586Z] [ACCESS] GET / - 304 - 77ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"77ms","timestamp":"2025-07-01T18:55:30.585Z"}
[2025-07-01T18:55:31.581Z] [ACCESS] GET /leaderboard - 304 - 81ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"81ms","timestamp":"2025-07-01T18:55:31.581Z"}
[2025-07-01T18:55:36.323Z] [ACCESS] GET / - 404 - 81ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"81ms","timestamp":"2025-07-01T18:55:36.323Z"}
[2025-07-01T18:55:45.857Z] [ACCESS] GET / - 304 - 79ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":304,"responseTime":"79ms","timestamp":"2025-07-01T18:55:45.856Z"}
[2025-07-01T18:55:45.963Z] [ACCESS] GET / - 404 - 80ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"80ms","timestamp":"2025-07-01T18:55:45.962Z"}
[2025-07-01T18:55:49.826Z] [ACCESS] GET / - 304 - 75ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":304,"responseTime":"75ms","timestamp":"2025-07-01T18:55:49.825Z"}
[2025-07-01T18:55:49.919Z] [ACCESS] GET / - 404 - 80ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"80ms","timestamp":"2025-07-01T18:55:49.919Z"}
[2025-07-01T18:55:54.348Z] [ACCESS] GET / - 304 - 75ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"75ms","timestamp":"2025-07-01T18:55:54.347Z"}
[2025-07-01T18:55:54.443Z] [ACCESS] GET / - 404 - 79ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"79ms","timestamp":"2025-07-01T18:55:54.442Z"}
[2025-07-01T18:55:56.894Z] [ACCESS] GET /gallery - 304 - 79ms | {"method":"GET","url":"/gallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"79ms","timestamp":"2025-07-01T18:55:56.894Z"}
[2025-07-01T18:55:57.046Z] [ACCESS] GET /check-student-auth - 200 - 77ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"77ms","timestamp":"2025-07-01T18:55:57.045Z"}
[2025-07-01T18:55:57.130Z] [ACCESS] GET / - 404 - 82ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"82ms","timestamp":"2025-07-01T18:55:57.130Z"}
[2025-07-01T18:55:59.146Z] [ACCESS] GET / - 404 - 87ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"87ms","timestamp":"2025-07-01T18:55:59.146Z"}
[2025-07-01T18:56:15.656Z] [ACCESS] GET / - 304 - 317ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"317ms","timestamp":"2025-07-01T18:56:15.655Z"}
[2025-07-01T18:56:15.759Z] [ACCESS] GET / - 404 - 72ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"72ms","timestamp":"2025-07-01T18:56:15.758Z"}
[2025-07-01T18:56:18.577Z] [ACCESS] GET /lessons - 304 - 69ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"69ms","timestamp":"2025-07-01T18:56:18.576Z"}
[2025-07-01T18:56:18.752Z] [ACCESS] GET /check-student-auth - 200 - 70ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"70ms","timestamp":"2025-07-01T18:56:18.750Z"}
[2025-07-01T18:56:20.811Z] [ACCESS] GET /lesson/1748074653639 - 304 - 69ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"69ms","timestamp":"2025-07-01T18:56:20.810Z"}
[2025-07-01T18:56:20.934Z] [ACCESS] GET /check-student-auth - 200 - 67ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T18:56:20.933Z"}
[2025-07-01T18:56:22.444Z] [ACCESS] GET / - 404 - 72ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"72ms","timestamp":"2025-07-01T18:56:22.443Z"}
[2025-07-01T18:56:40.638Z] [ACCESS] GET /admin/login - 200 - 303ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"303ms","timestamp":"2025-07-01T18:56:40.637Z"}
[2025-07-01T18:56:42.914Z] [ACCESS] POST /admin/login - 200 - 81ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"81ms","timestamp":"2025-07-01T18:56:42.913Z"}
[2025-07-01T18:56:43.504Z] [ACCESS] GET /admin - 200 - 71ms | {"method":"GET","url":"/admin","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"71ms","timestamp":"2025-07-01T18:56:43.502Z"}
[2025-07-01T18:56:43.950Z] [ACCESS] GET /?page=1&limit=15&sort=az - 304 - 401ms | {"method":"GET","url":"/?page=1&limit=15&sort=az","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"401ms","timestamp":"2025-07-01T18:56:43.949Z"}
[2025-07-01T18:56:47.057Z] [ACCESS] GET / - 404 - 73ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"73ms","timestamp":"2025-07-01T18:56:47.057Z"}
[2025-07-01T18:56:49.031Z] [ACCESS] GET / - 404 - 71ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"71ms","timestamp":"2025-07-01T18:56:49.029Z"}
[2025-07-01T18:57:06.282Z] [ACCESS] GET /admin/students - 200 - 234ms | {"method":"GET","url":"/admin/students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"234ms","timestamp":"2025-07-01T18:57:06.281Z"}
[2025-07-01T18:57:06.402Z] [ACCESS] GET / - 404 - 62ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"62ms","timestamp":"2025-07-01T18:57:06.401Z"}
[2025-07-01T18:57:06.599Z] [ACCESS] GET / - 404 - 259ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"259ms","timestamp":"2025-07-01T18:57:06.599Z"}
[2025-07-01T18:57:09.348Z] [ACCESS] GET / - 404 - 65ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"65ms","timestamp":"2025-07-01T18:57:09.347Z"}
[2025-07-01T18:57:19.864Z] [ACCESS] GET /admin - 200 - 336ms | {"method":"GET","url":"/admin","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"336ms","timestamp":"2025-07-01T18:57:19.863Z"}
[2025-07-01T18:57:19.935Z] [ACCESS] GET / - 404 - 64ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"64ms","timestamp":"2025-07-01T18:57:19.934Z"}
[2025-07-01T18:57:23.141Z] [ACCESS] GET /history - 304 - 62ms | {"method":"GET","url":"/history","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"62ms","timestamp":"2025-07-01T18:57:23.140Z"}
[2025-07-01T18:57:23.995Z] [ACCESS] GET /?page=1&limit=15&sort=time-desc - 200 - 610ms | {"method":"GET","url":"/?page=1&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"610ms","timestamp":"2025-07-01T18:57:23.993Z"}
[2025-07-01T18:57:30.265Z] [ACCESS] GET / - 404 - 146ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"146ms","timestamp":"2025-07-01T18:57:30.264Z"}
[2025-07-01T18:57:36.589Z] [ACCESS] GET / - 404 - 65ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"65ms","timestamp":"2025-07-01T18:57:36.588Z"}
[2025-07-01T18:57:42.736Z] [ACCESS] GET / - 404 - 66ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"66ms","timestamp":"2025-07-01T18:57:42.735Z"}
[2025-07-01T18:57:45.020Z] [ACCESS] GET / - 404 - 63ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"63ms","timestamp":"2025-07-01T18:57:45.019Z"}
[2025-07-01T18:58:51.840Z] [ACCESS] GET / - 304 - 370ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"370ms","timestamp":"2025-07-01T18:58:51.839Z"}
[2025-07-01T18:59:20.629Z] [ACCESS] GET / - 404 - 276ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"276ms","timestamp":"2025-07-01T18:59:20.629Z"}
[2025-07-01T18:59:23.679Z] [ACCESS] GET /quizgame - 200 - 67ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T18:59:23.677Z"}
[2025-07-01T18:59:23.944Z] [ACCESS] GET / - 404 - 271ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"271ms","timestamp":"2025-07-01T18:59:23.944Z"}
[2025-07-01T18:59:26.832Z] [ACCESS] GET / - 404 - 70ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"70ms","timestamp":"2025-07-01T18:59:26.831Z"}
[2025-07-01T18:59:26.873Z] [ACCESS] GET / - 404 - 74ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"74ms","timestamp":"2025-07-01T18:59:26.873Z"}
[2025-07-01T18:59:27.738Z] [ACCESS] GET /quizgame - 200 - 69ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-01T18:59:27.737Z"}
[2025-07-01T18:59:27.808Z] [ACCESS] GET / - 404 - 74ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"74ms","timestamp":"2025-07-01T18:59:27.806Z"}
[2025-07-01T18:59:30.391Z] [ACCESS] GET / - 404 - 74ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"74ms","timestamp":"2025-07-01T18:59:30.391Z"}
[2025-07-01T18:59:30.431Z] [ACCESS] GET / - 404 - 72ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"72ms","timestamp":"2025-07-01T18:59:30.431Z"}
[2025-07-01T18:59:32.003Z] [ACCESS] GET /leaderboard - 200 - 67ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T18:59:32.003Z"}
[2025-07-01T18:59:37.532Z] [ACCESS] GET / - 404 - 69ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"69ms","timestamp":"2025-07-01T18:59:37.532Z"}
[2025-07-01T18:59:41.022Z] [ACCESS] GET /?page=1&filter=month - 200 - 568ms | {"method":"GET","url":"/?page=1&filter=month","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"568ms","timestamp":"2025-07-01T18:59:41.021Z"}
[2025-07-01T18:59:43.229Z] [ACCESS] GET /?page=1&filter=week - 200 - 123ms | {"method":"GET","url":"/?page=1&filter=week","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"123ms","timestamp":"2025-07-01T18:59:43.214Z"}
[2025-07-01T18:59:49.265Z] [ACCESS] GET /?page=2&filter=all - 200 - 189ms | {"method":"GET","url":"/?page=2&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"189ms","timestamp":"2025-07-01T18:59:49.264Z"}
[2025-07-01T19:00:01.224Z] [ACCESS] GET / - 304 - 646ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"646ms","timestamp":"2025-07-01T19:00:01.223Z"}
[2025-07-01T19:00:03.452Z] [ACCESS] GET /lessons - 304 - 73ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"73ms","timestamp":"2025-07-01T19:00:03.451Z"}
[2025-07-01T19:00:03.574Z] [ACCESS] GET /check-student-auth - 200 - 72ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"72ms","timestamp":"2025-07-01T19:00:03.573Z"}
[2025-07-01T19:00:15.040Z] [ACCESS] GET / - 304 - 657ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"657ms","timestamp":"2025-07-01T19:00:15.040Z"}
[2025-07-01T19:00:16.434Z] [ACCESS] GET /lessons - 304 - 76ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"76ms","timestamp":"2025-07-01T19:00:16.433Z"}
[2025-07-01T19:00:16.556Z] [ACCESS] GET /check-student-auth - 200 - 81ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"81ms","timestamp":"2025-07-01T19:00:16.555Z"}
[2025-07-01T19:00:19.092Z] [ACCESS] GET /lesson/1748074653639 - 304 - 77ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"77ms","timestamp":"2025-07-01T19:00:19.091Z"}
[2025-07-01T19:00:19.241Z] [ACCESS] GET /check-student-auth - 200 - 83ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"83ms","timestamp":"2025-07-01T19:00:19.241Z"}
[2025-07-01T19:00:22.198Z] [ACCESS] GET / - 304 - 80ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"80ms","timestamp":"2025-07-01T19:00:22.197Z"}
[2025-07-01T19:00:22.924Z] [ACCESS] GET /gallery - 304 - 78ms | {"method":"GET","url":"/gallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"78ms","timestamp":"2025-07-01T19:00:22.924Z"}
[2025-07-01T19:00:23.034Z] [ACCESS] GET /check-student-auth - 200 - 77ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"77ms","timestamp":"2025-07-01T19:00:23.033Z"}
[2025-07-01T19:00:23.123Z] [ACCESS] GET / - 404 - 85ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"85ms","timestamp":"2025-07-01T19:00:23.122Z"}
[2025-07-01T19:00:26.118Z] [ACCESS] GET / - 304 - 80ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"80ms","timestamp":"2025-07-01T19:00:26.117Z"}
[2025-07-01T19:00:27.764Z] [ACCESS] GET /leaderboard - 200 - 76ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"76ms","timestamp":"2025-07-01T19:00:27.763Z"}
[2025-07-01T19:00:28.719Z] [ACCESS] GET / - 304 - 77ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"77ms","timestamp":"2025-07-01T19:00:28.718Z"}
[2025-07-01T19:01:08.991Z] [ACCESS] GET /quizgame - 200 - 274ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"274ms","timestamp":"2025-07-01T19:01:08.990Z"}
[2025-07-01T19:01:11.145Z] [ACCESS] GET / - 404 - 68ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"68ms","timestamp":"2025-07-01T19:01:11.144Z"}
[2025-07-01T19:01:23.854Z] [ACCESS] GET / - 304 - 279ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"279ms","timestamp":"2025-07-01T19:01:23.853Z"}
[2025-07-01T19:01:29.461Z] [ACCESS] GET /lessons - 304 - 67ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"67ms","timestamp":"2025-07-01T19:01:29.461Z"}
[2025-07-01T19:01:29.583Z] [ACCESS] GET /check-student-auth - 200 - 70ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"70ms","timestamp":"2025-07-01T19:01:29.583Z"}
[2025-07-01T19:01:32.285Z] [ACCESS] GET / - 304 - 77ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"77ms","timestamp":"2025-07-01T19:01:32.284Z"}
[2025-07-01T19:01:33.524Z] [ACCESS] GET /quizgame - 200 - 94ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"94ms","timestamp":"2025-07-01T19:01:33.523Z"}
[2025-07-01T19:01:34.626Z] [ACCESS] GET / - 304 - 67ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"67ms","timestamp":"2025-07-01T19:01:34.625Z"}
[2025-07-01T19:01:35.219Z] [ACCESS] GET /lessons - 304 - 69ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"69ms","timestamp":"2025-07-01T19:01:35.218Z"}
[2025-07-01T19:01:35.335Z] [ACCESS] GET /check-student-auth - 200 - 69ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-01T19:01:35.335Z"}
[2025-07-01T19:10:36.575Z] [ACCESS] GET / - 304 - 288ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"288ms","timestamp":"2025-07-01T19:10:36.574Z"}
[2025-07-01T19:10:41.209Z] [ACCESS] GET /quizgame - 200 - 374ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"374ms","timestamp":"2025-07-01T19:10:41.208Z"}
[2025-07-01T19:10:44.347Z] [ACCESS] GET /leaderboard - 304 - 324ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"324ms","timestamp":"2025-07-01T19:10:44.346Z"}
[2025-07-01T19:10:55.485Z] [ACCESS] GET / - 404 - 550ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"550ms","timestamp":"2025-07-01T19:10:55.485Z"}
[2025-07-01T19:10:59.597Z] [ACCESS] GET /gallery - 304 - 580ms | {"method":"GET","url":"/gallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"580ms","timestamp":"2025-07-01T19:10:59.596Z"}
[2025-07-01T19:10:59.787Z] [ACCESS] GET /check-student-auth - 200 - 74ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"74ms","timestamp":"2025-07-01T19:10:59.786Z"}
[2025-07-01T19:10:59.884Z] [ACCESS] GET / - 404 - 92ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"92ms","timestamp":"2025-07-01T19:10:59.884Z"}
[2025-07-01T19:11:00.762Z] [ACCESS] GET /?page=1&filter=all - 304 - 16372ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"16372ms","timestamp":"2025-07-01T19:11:00.761Z"}
[2025-07-01T19:11:02.185Z] [ACCESS] GET /lessons - 304 - 69ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"69ms","timestamp":"2025-07-01T19:11:02.185Z"}
[2025-07-01T19:11:02.296Z] [ACCESS] GET /check-student-auth - 200 - 68ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-01T19:11:02.295Z"}
[2025-07-01T19:11:02.772Z] [ACCESS] GET /?page=1&limit=10&sort=newest - 200 - 469ms | {"method":"GET","url":"/?page=1&limit=10&sort=newest","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"469ms","timestamp":"2025-07-01T19:11:02.771Z"}
[2025-07-01T19:11:04.721Z] [ACCESS] GET /lesson/1748074653639 - 304 - 68ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"68ms","timestamp":"2025-07-01T19:11:04.720Z"}
[2025-07-01T19:11:04.855Z] [ACCESS] GET /check-student-auth - 200 - 74ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"74ms","timestamp":"2025-07-01T19:11:04.854Z"}
[2025-07-01T19:11:05.067Z] [ACCESS] GET /1748074653639 - 200 - 209ms | {"method":"GET","url":"/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"209ms","timestamp":"2025-07-01T19:11:05.066Z"}
[2025-07-01T19:11:16.318Z] [ACCESS] GET /admin/login - 200 - 289ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"289ms","timestamp":"2025-07-01T19:11:16.310Z"}
[2025-07-01T19:11:18.498Z] [ACCESS] POST /admin/login - 200 - 84ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"84ms","timestamp":"2025-07-01T19:11:18.498Z"}
[2025-07-01T19:11:19.080Z] [ACCESS] GET /admin - 200 - 71ms | {"method":"GET","url":"/admin","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"71ms","timestamp":"2025-07-01T19:11:19.080Z"}
[2025-07-01T19:11:20.692Z] [ACCESS] GET /?page=1&limit=15&sort=az - 304 - 1603ms | {"method":"GET","url":"/?page=1&limit=15&sort=az","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"1603ms","timestamp":"2025-07-01T19:11:20.692Z"}
[2025-07-01T19:11:21.729Z] [ACCESS] GET /admin/new - 200 - 73ms | {"method":"GET","url":"/admin/new","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"73ms","timestamp":"2025-07-01T19:11:21.725Z"}
[2025-07-01T19:11:21.737Z] [ACCESS] GET /js/admin-stage1-editor.js - 200 - 15ms | {"method":"GET","url":"/js/admin-stage1-editor.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"15ms","timestamp":"2025-07-01T19:11:21.736Z"}
[2025-07-01T19:11:21.740Z] [ACCESS] GET /js/document-upload.js - 200 - 4ms | {"method":"GET","url":"/js/document-upload.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T19:11:21.739Z"}
[2025-07-01T19:11:21.741Z] [ACCESS] GET /js/storage-recovery.js - 200 - 6ms | {"method":"GET","url":"/js/storage-recovery.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-01T19:11:21.741Z"}
[2025-07-01T19:11:32.346Z] [ACCESS] GET / - 304 - 277ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"277ms","timestamp":"2025-07-01T19:11:32.345Z"}
[2025-07-01T19:11:36.714Z] [ACCESS] GET /lessons - 304 - 63ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"63ms","timestamp":"2025-07-01T19:11:36.714Z"}
[2025-07-01T19:11:36.830Z] [ACCESS] GET /check-student-auth - 200 - 74ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"74ms","timestamp":"2025-07-01T19:11:36.829Z"}
[2025-07-01T19:11:38.627Z] [ACCESS] GET /admin/login - 200 - 570ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"570ms","timestamp":"2025-07-01T19:11:38.625Z"}
[2025-07-01T19:11:39.730Z] [ACCESS] GET / - 304 - 63ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"63ms","timestamp":"2025-07-01T19:11:39.729Z"}
[2025-07-01T19:11:41.412Z] [ACCESS] GET /lessons - 304 - 64ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"64ms","timestamp":"2025-07-01T19:11:41.411Z"}
[2025-07-01T19:11:41.511Z] [ACCESS] GET /check-student-auth - 200 - 63ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"63ms","timestamp":"2025-07-01T19:11:41.510Z"}
[2025-07-01T19:11:42.647Z] [ACCESS] GET /admin/login - 200 - 64ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"64ms","timestamp":"2025-07-01T19:11:42.647Z"}
[2025-07-01T19:11:45.742Z] [ACCESS] POST /admin/login - 200 - 65ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"65ms","timestamp":"2025-07-01T19:11:45.742Z"}
[2025-07-01T19:11:46.333Z] [ACCESS] GET /admin - 200 - 64ms | {"method":"GET","url":"/admin","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"64ms","timestamp":"2025-07-01T19:11:46.331Z"}
[2025-07-01T19:11:53.848Z] [ACCESS] GET /share/lesson/1744597118421 - 200 - 425ms | {"method":"GET","url":"/share/lesson/1744597118421","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"425ms","timestamp":"2025-07-01T19:11:53.847Z"}
[2025-07-01T19:12:03.380Z] [ACCESS] GET /?page=2&limit=15&sort=az - 200 - 451ms | {"method":"GET","url":"/?page=2&limit=15&sort=az","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"451ms","timestamp":"2025-07-01T19:12:03.379Z"}
[2025-07-01T19:12:08.369Z] [ACCESS] GET /?page=1&limit=15&sort=az&search=chuong3 - 200 - 378ms | {"method":"GET","url":"/?page=1&limit=15&sort=az&search=chuong3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"378ms","timestamp":"2025-07-01T19:12:08.368Z"}
[2025-07-01T19:12:14.543Z] [ACCESS] PUT /1743788630452 - 400 - 64ms | {"method":"PUT","url":"/1743788630452","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"64ms","timestamp":"2025-07-01T19:12:14.542Z"}
[2025-07-01T19:12:21.127Z] [ACCESS] GET / - 404 - 70ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"70ms","timestamp":"2025-07-01T19:12:21.126Z"}
[2025-07-01T19:13:26.502Z] [ACCESS] GET / - 304 - 266ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"266ms","timestamp":"2025-07-01T19:13:26.502Z"}
[2025-07-01T19:14:06.031Z] [ACCESS] GET / - 200 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T19:14:06.030Z"}
[2025-07-01T19:14:06.069Z] [ACCESS] GET /css/style.css - 200 - 3ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T19:14:06.068Z"}
[2025-07-01T19:14:06.071Z] [ACCESS] GET /images/lesson1.jpg - 200 - 5ms | {"method":"GET","url":"/images/lesson1.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-01T19:14:06.070Z"}
[2025-07-01T19:14:06.072Z] [ACCESS] GET /images/lesson2.jpg - 200 - 6ms | {"method":"GET","url":"/images/lesson2.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-01T19:14:06.072Z"}
[2025-07-01T19:14:06.115Z] [ACCESS] GET /images/lesson3.jpg - 200 - 3ms | {"method":"GET","url":"/images/lesson3.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T19:14:06.115Z"}
[2025-07-01T19:14:06.119Z] [ACCESS] GET /images/lesson4.jpg - 200 - 3ms | {"method":"GET","url":"/images/lesson4.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T19:14:06.119Z"}
[2025-07-01T19:14:06.121Z] [ACCESS] GET /js/network-animation.js - 200 - 3ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T19:14:06.120Z"}
[2025-07-01T19:14:06.123Z] [ACCESS] GET /js/landing.js - 200 - 4ms | {"method":"GET","url":"/js/landing.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T19:14:06.122Z"}
[2025-07-01T19:14:19.721Z] [ACCESS] GET / - 304 - 9ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"9ms","timestamp":"2025-07-01T19:14:19.720Z"}
[2025-07-01T19:14:21.074Z] [ACCESS] GET /lessons - 200 - 6ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-01T19:14:21.074Z"}
[2025-07-01T19:14:21.093Z] [ACCESS] GET /js/index.js - 200 - 2ms | {"method":"GET","url":"/js/index.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T19:14:21.092Z"}
[2025-07-01T19:14:21.133Z] [ACCESS] GET /check-student-auth - 200 - 4ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T19:14:21.132Z"}
[2025-07-01T19:14:21.921Z] [ACCESS] GET / - 200 - 784ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"784ms","timestamp":"2025-07-01T19:14:21.919Z"}
[2025-07-01T19:14:21.992Z] [ACCESS] GET /?page=1&limit=10&sort=newest - 200 - 65ms | {"method":"GET","url":"/?page=1&limit=10&sort=newest","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"65ms","timestamp":"2025-07-01T19:14:21.991Z"}
[2025-07-01T19:14:42.752Z] [ACCESS] GET /?page=1&limit=10&sort=oldest - 200 - 299ms | {"method":"GET","url":"/?page=1&limit=10&sort=oldest","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"299ms","timestamp":"2025-07-01T19:14:42.751Z"}
[2025-07-01T19:15:24.759Z] [ACCESS] GET / - 404 - 4ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T19:15:24.759Z"}
[2025-07-01T19:15:38.923Z] [ACCESS] GET /lesson/1738741795156 - 200 - 2ms | {"method":"GET","url":"/lesson/1738741795156","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T19:15:38.922Z"}
[2025-07-01T19:15:38.956Z] [ACCESS] GET /js/lesson.js - 200 - 2ms | {"method":"GET","url":"/js/lesson.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T19:15:38.956Z"}
[2025-07-01T19:15:39.172Z] [ACCESS] GET /check-student-auth - 200 - 2ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T19:15:39.172Z"}
[2025-07-01T19:15:39.464Z] [ACCESS] GET /1738741795156 - 200 - 288ms | {"method":"GET","url":"/1738741795156","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"288ms","timestamp":"2025-07-01T19:15:39.463Z"}
[2025-07-01T19:18:55.953Z] [ACCESS] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T19:18:55.952Z"}
[2025-07-01T19:18:59.757Z] [ACCESS] GET /student/login?redirect=%2Flesson%2F1738741795156 - 200 - 3ms | {"method":"GET","url":"/student/login?redirect=%2Flesson%2F1738741795156","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T19:18:59.757Z"}
[2025-07-01T19:18:59.798Z] [ACCESS] GET /js/device-id.js - 200 - 3ms | {"method":"GET","url":"/js/device-id.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T19:18:59.796Z"}
[2025-07-01T19:19:04.460Z] [ACCESS] POST /student/login - 200 - 1013ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1013ms","timestamp":"2025-07-01T19:19:04.458Z"}
[2025-07-01T19:19:05.053Z] [ACCESS] GET /lesson/1738741795156 - 304 - 65ms | {"method":"GET","url":"/lesson/1738741795156","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"65ms","timestamp":"2025-07-01T19:19:05.052Z"}
[2025-07-01T19:19:05.168Z] [ACCESS] GET /check-student-auth - 200 - 63ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"63ms","timestamp":"2025-07-01T19:19:05.167Z"}
[2025-07-01T19:19:06.731Z] [ACCESS] GET /check-student-auth - 200 - 66ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"66ms","timestamp":"2025-07-01T19:19:06.729Z"}
[2025-07-01T19:19:17.603Z] [ACCESS] GET / - 404 - 230ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"230ms","timestamp":"2025-07-01T19:19:17.602Z"}
[2025-07-01T19:22:29.519Z] [ACCESS] GET / - 304 - 245ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"245ms","timestamp":"2025-07-01T19:22:29.518Z"}
[2025-07-01T19:22:30.565Z] [ACCESS] GET /lessons - 304 - 67ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"67ms","timestamp":"2025-07-01T19:22:30.564Z"}
[2025-07-01T19:22:30.676Z] [ACCESS] GET /check-student-auth - 200 - 60ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"60ms","timestamp":"2025-07-01T19:22:30.675Z"}
[2025-07-01T19:22:32.432Z] [ACCESS] GET /lessons - 304 - 61ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"61ms","timestamp":"2025-07-01T19:22:32.431Z"}
[2025-07-01T19:22:32.534Z] [ACCESS] GET /check-student-auth - 200 - 59ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"59ms","timestamp":"2025-07-01T19:22:32.533Z"}
[2025-07-01T19:22:33.797Z] [ACCESS] GET /lesson/1748074653639 - 200 - 60ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"60ms","timestamp":"2025-07-01T19:22:33.797Z"}
[2025-07-01T19:22:33.898Z] [ACCESS] GET /check-student-auth - 200 - 59ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"59ms","timestamp":"2025-07-01T19:22:33.897Z"}
[2025-07-01T19:22:34.493Z] [ACCESS] GET /1748074653639 - 200 - 589ms | {"method":"GET","url":"/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"589ms","timestamp":"2025-07-01T19:22:34.489Z"}
[2025-07-01T19:22:53.592Z] [ACCESS] GET /check-student-auth - 200 - 363ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"363ms","timestamp":"2025-07-01T19:22:53.591Z"}
[2025-07-01T19:24:20.745Z] [ACCESS] GET /lessons - 304 - 263ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"263ms","timestamp":"2025-07-01T19:24:20.744Z"}
[2025-07-01T19:24:20.891Z] [ACCESS] GET /check-student-auth - 200 - 71ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"71ms","timestamp":"2025-07-01T19:24:20.881Z"}
[2025-07-01T19:24:22.241Z] [ACCESS] GET /lesson/1748074653639 - 200 - 68ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-01T19:24:22.238Z"}
[2025-07-01T19:24:22.247Z] [ACCESS] GET /css/lesson-questions.css - 200 - 6ms | {"method":"GET","url":"/css/lesson-questions.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-01T19:24:22.241Z"}
[2025-07-01T19:24:22.353Z] [ACCESS] GET /check-student-auth - 200 - 65ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"65ms","timestamp":"2025-07-01T19:24:22.349Z"}
[2025-07-01T19:24:38.036Z] [ACCESS] GET /check-student-auth - 200 - 296ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"296ms","timestamp":"2025-07-01T19:24:38.035Z"}
[2025-07-01T19:24:38.345Z] [ACCESS] GET /?page=1&limit=10&sort=newest - 200 - 294ms | {"method":"GET","url":"/?page=1&limit=10&sort=newest","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"294ms","timestamp":"2025-07-01T19:24:38.337Z"}
[2025-07-01T19:27:38.533Z] [ACCESS] GET /lesson/1748074653639 - 200 - 238ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"238ms","timestamp":"2025-07-01T19:27:38.531Z"}
[2025-07-01T19:27:38.669Z] [ACCESS] GET /check-student-auth - 200 - 54ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"54ms","timestamp":"2025-07-01T19:27:38.668Z"}
[2025-07-01T19:28:01.437Z] [ACCESS] GET /check-student-auth - 200 - 293ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"293ms","timestamp":"2025-07-01T19:28:01.436Z"}
[2025-07-01T19:28:04.999Z] [ACCESS] GET /?page=1&limit=10&sort=popular - 200 - 639ms | {"method":"GET","url":"/?page=1&limit=10&sort=popular","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"639ms","timestamp":"2025-07-01T19:28:04.999Z"}
[2025-07-01T19:28:08.093Z] [ACCESS] GET /lesson/1743697727460 - 200 - 72ms | {"method":"GET","url":"/lesson/1743697727460","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"72ms","timestamp":"2025-07-01T19:28:08.092Z"}
[2025-07-01T19:28:08.180Z] [ACCESS] GET /check-student-auth - 200 - 74ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"74ms","timestamp":"2025-07-01T19:28:08.179Z"}
[2025-07-01T19:28:08.404Z] [ACCESS] GET /1743697727460 - 200 - 221ms | {"method":"GET","url":"/1743697727460","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"221ms","timestamp":"2025-07-01T19:28:08.403Z"}
[2025-07-01T19:28:28.187Z] [ACCESS] GET /lesson/1743697727460 - 200 - 237ms | {"method":"GET","url":"/lesson/1743697727460","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"237ms","timestamp":"2025-07-01T19:28:28.186Z"}
[2025-07-01T19:28:28.288Z] [ACCESS] GET /check-student-auth - 200 - 61ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"61ms","timestamp":"2025-07-01T19:28:28.287Z"}
[2025-07-01T19:28:32.145Z] [ACCESS] GET / - 304 - 61ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"61ms","timestamp":"2025-07-01T19:28:32.144Z"}
[2025-07-01T19:28:36.243Z] [ACCESS] GET /quizgame - 200 - 73ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"73ms","timestamp":"2025-07-01T19:28:36.242Z"}
[2025-07-01T19:28:36.245Z] [ACCESS] GET /js/quizgame.js - 200 - 4ms | {"method":"GET","url":"/js/quizgame.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T19:28:36.244Z"}
[2025-07-01T19:28:36.416Z] [ACCESS] GET /audio/5sec_1.mp3 - 206 - 8ms | {"method":"GET","url":"/audio/5sec_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"8ms","timestamp":"2025-07-01T19:28:36.415Z"}
[2025-07-01T19:28:36.421Z] [ACCESS] GET /audio/5sec_2.mp3 - 206 - 11ms | {"method":"GET","url":"/audio/5sec_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"11ms","timestamp":"2025-07-01T19:28:36.420Z"}
[2025-07-01T19:28:36.423Z] [ACCESS] GET /audio/5sec_3.mp3 - 206 - 12ms | {"method":"GET","url":"/audio/5sec_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"12ms","timestamp":"2025-07-01T19:28:36.422Z"}
[2025-07-01T19:28:36.429Z] [ACCESS] GET /audio/30sec_1.mp3 - 206 - 28ms | {"method":"GET","url":"/audio/30sec_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"28ms","timestamp":"2025-07-01T19:28:36.428Z"}
[2025-07-01T19:28:36.437Z] [ACCESS] GET /audio/30sec_2.mp3 - 206 - 33ms | {"method":"GET","url":"/audio/30sec_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"33ms","timestamp":"2025-07-01T19:28:36.435Z"}
[2025-07-01T19:28:36.442Z] [ACCESS] GET /audio/correct_1.mp3 - 206 - 20ms | {"method":"GET","url":"/audio/correct_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"20ms","timestamp":"2025-07-01T19:28:36.439Z"}
[2025-07-01T19:28:36.444Z] [ACCESS] GET /audio/30sec_3.mp3 - 206 - 39ms | {"method":"GET","url":"/audio/30sec_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"39ms","timestamp":"2025-07-01T19:28:36.443Z"}
[2025-07-01T19:28:36.448Z] [ACCESS] GET /audio/correct_2.mp3 - 206 - 15ms | {"method":"GET","url":"/audio/correct_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"15ms","timestamp":"2025-07-01T19:28:36.447Z"}
[2025-07-01T19:28:36.453Z] [ACCESS] GET /audio/correct_3.mp3 - 206 - 18ms | {"method":"GET","url":"/audio/correct_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"18ms","timestamp":"2025-07-01T19:28:36.450Z"}
[2025-07-01T19:28:36.456Z] [ACCESS] GET /audio/correct_4.mp3 - 206 - 22ms | {"method":"GET","url":"/audio/correct_4.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"22ms","timestamp":"2025-07-01T19:28:36.456Z"}
[2025-07-01T19:28:36.459Z] [ACCESS] GET /audio/correct_5.mp3 - 206 - 4ms | {"method":"GET","url":"/audio/correct_5.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"4ms","timestamp":"2025-07-01T19:28:36.458Z"}
[2025-07-01T19:28:36.461Z] [ACCESS] GET /audio/incorrect.mp3 - 206 - 6ms | {"method":"GET","url":"/audio/incorrect.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"6ms","timestamp":"2025-07-01T19:28:36.460Z"}
[2025-07-01T19:28:36.463Z] [ACCESS] GET /audio/points.mp3 - 206 - 7ms | {"method":"GET","url":"/audio/points.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"7ms","timestamp":"2025-07-01T19:28:36.462Z"}
[2025-07-01T19:28:39.050Z] [ACCESS] GET / - 304 - 59ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"59ms","timestamp":"2025-07-01T19:28:39.049Z"}
